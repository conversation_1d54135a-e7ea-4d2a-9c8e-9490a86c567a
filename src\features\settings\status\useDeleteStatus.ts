import { statusService } from "@/services/statusService";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export const useDeleteStatus = () =>{
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id:number) => statusService.deleteStatus(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["useStatus"] });
            toast.success("Đã xóa trạng thái thành công!");
        },
        onError: (error: Error) => {
            toast.error(`Lỗi khi xóa trạng thái: ${error.message}`);
        },
    })
}