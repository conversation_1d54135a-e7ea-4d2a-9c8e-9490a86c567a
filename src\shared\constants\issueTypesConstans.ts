// Issue type validation constants
export const ISSUETYPE_VALIDATION = {
  NAME_MIN_LENGTH: 1,
  NAME_MAX_LENGTH: 50,
  DESCRIPTION_MAX_LENGTH: 500,
  ORDER_MIN: 1,
  ORDER_MAX: 9999,
  MAX_TAGS: 5,
  TAG_MAX_LENGTH: 20,
  MAX_BULK_OPERATIONS: 50,
  MAX_REORDER_ITEMS: 100,
} as const;

// Default issue type colors
export const DEFAULT_ISSUETYPE_COLORS = [
  "#3B82F6", // Blue
  "#10B981", // Green
  "#EF4444", // Red
  "#F59E0B", // Yellow
  "#8B5CF6", // Purple
  "#06B6D4", // Cyan
  "#EC4899", // Pink
  "#84CC16", // Lime
  "#F97316", // Orange
  "#6B7280", // Gray
  "#1F2937", // Dark Gray
  "#7C3AED", // Violet
  "#DC2626", // Red Dark
  "#059669", // Green Dark
  "#0EA5E9", // Sky Blue
] as const;

// Issue type status options
export const ISSUETYPE_STATUS = {
  ACTIVE: true,
  INACTIVE: false,
} as const;

// Sort field options
export const ISSUETYPE_SORT_FIELDS = [
  "name",
  "createdAt",
  "updatedAt",
  "order",
  "issueCount",
] as const;

// Sort direction options
export const SORT_DIRECTIONS = ["asc", "desc"] as const;

// Pagination defaults
export const PAGINATION_DEFAULTS = {
  PAGE: 1,
  LIMIT: 10,
  MAX_LIMIT: 100,
} as const;

// Color brightness validation
export const COLOR_VALIDATION = {
  MIN_BRIGHTNESS: 30,
  MAX_BRIGHTNESS: 225,
} as const;

// Issue type form field names
export const ISSUETYPE_FORM_FIELDS = {
  NAME: "name",
  DESCRIPTION: "description",
  COLOR: "color",
  IS_ACTIVE: "isActive",
  PROJECT_ID: "projectId",
  TAGS: "tags",
  ORDER: "order",
} as const;

// Error messages
export const ISSUETYPE_ERROR_MESSAGES = {
  NAME_REQUIRED: "Tên loại vấn đề không được để trống",
  NAME_TOO_LONG: `Tên loại vấn đề không được quá ${ISSUETYPE_VALIDATION.NAME_MAX_LENGTH} ký tự`,
  NAME_INVALID_CHARS: "Tên loại vấn đề chỉ được chứa chữ cái, số, khoảng trắng và các ký tự: - _ .",
  NAME_ONLY_WHITESPACE: "Tên loại vấn đề không được chỉ chứa khoảng trắng",
  DESCRIPTION_TOO_LONG: `Mô tả không được quá ${ISSUETYPE_VALIDATION.DESCRIPTION_MAX_LENGTH} ký tự`,
  COLOR_INVALID_FORMAT: "Màu sắc phải là mã hex hợp lệ (ví dụ: #FF0000)",
  COLOR_INVALID_BRIGHTNESS: "Màu sắc không phù hợp cho hiển thị text",
  ID_INVALID: "ID loại vấn đề không hợp lệ",
  ID_INVALID_UUID: "ID loại vấn đề phải là UUID hợp lệ",
  ORDER_INVALID: `Thứ tự phải từ ${ISSUETYPE_VALIDATION.ORDER_MIN} đến ${ISSUETYPE_VALIDATION.ORDER_MAX}`,
  TAGS_TOO_MANY: `Không được có quá ${ISSUETYPE_VALIDATION.MAX_TAGS} tags`,
  TAG_TOO_LONG: `Mỗi tag không được quá ${ISSUETYPE_VALIDATION.TAG_MAX_LENGTH} ký tự`,
  BULK_TOO_MANY: `Không thể thao tác quá ${ISSUETYPE_VALIDATION.MAX_BULK_OPERATIONS} loại vấn đề cùng lúc`,
  REORDER_TOO_MANY: `Không thể sắp xếp quá ${ISSUETYPE_VALIDATION.MAX_REORDER_ITEMS} loại vấn đề cùng lúc`,
  ISSUETYPE_IN_USE: "Không thể xóa loại vấn đề đang được sử dụng",
  ISSUETYPE_NOT_FOUND: "Loại vấn đề không tồn tại",
  ISSUETYPE_NAME_EXISTS: "Tên loại vấn đề đã tồn tại",
} as const;