import { z } from "zod";
import {
  ISSUETYPE_VALIDATION,
  ISSUETYPE_ERROR_MESSAGES,
} from "@/shared/constants/issueTypesConstans";

export const issueTypeFormSchema = z.object({
  name: z
    .string()
    .min(ISSUETYPE_VALIDATION.NAME_MIN_LENGTH, ISSUETYPE_ERROR_MESSAGES.NAME_REQUIRED)
    .max(ISSUETYPE_VALIDATION.NAME_MAX_LENGTH, ISSUETYPE_ERROR_MESSAGES.NAME_TOO_LONG)
    .regex(
      /^[a-zA-Z0-9\s\u00C0-\u024F\u1E00-\u1EFF\-_\.]+$/,
      ISSUETYPE_ERROR_MESSAGES.NAME_INVALID_CHARS
    ),
  description: z
    .string()
    .max(
      ISSUETYPE_VALIDATION.DESCRIPTION_MAX_LENGTH,
      ISSUETYPE_ERROR_MESSAGES.DESCRIPTION_TOO_LONG
    )
    .optional(),

  color: z
    .string()
    .min(1, ISSUETYPE_ERROR_MESSAGES.COLOR_INVALID_FORMAT)
    .regex(/^#[0-9A-Fa-f]{6}$/, ISSUETYPE_ERROR_MESSAGES.COLOR_INVALID_FORMAT),
  order: z
    .number()
    .int()
    .min(ISSUETYPE_VALIDATION.ORDER_MIN, ISSUETYPE_ERROR_MESSAGES.ORDER_INVALID),
  active: z.boolean(),
});

export type IssueTypeFormValues = z.infer<typeof issueTypeFormSchema>;
