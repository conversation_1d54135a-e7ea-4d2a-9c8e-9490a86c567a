export type Status = {
  id: number;
  name: string;
  description?: string;
  color: string;
  order: number;
  projectId: number;
  projectKey: string;
  isDeleted: boolean;
  createdAt: Date;
  createdById: number;
  lastModifiedAt?: Date;
  lastModifiedById?: number;
  deletedAt?: Date;
  deletedById?: number;
};

// Type cho việc tạo status mới (không cần id và các field tự động)
export type CreateStatusRequest = {
  name: string;
  description?: string;
  color: string;
  order: number;
  active: boolean;
};
