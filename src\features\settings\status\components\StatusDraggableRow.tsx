import { TableCell, TableRow } from "@/components/ui/table";
import { Status } from "@/shared/types/status";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { flexRender, Row } from "@tanstack/react-table";
import { GripVertical } from "lucide-react";

export function StatusDraggableRow({ row, statusId }: { row: Row<Status>; statusId: string }) {
  const { setNodeRef, attributes, listeners, transform, transition, isDragging } =
    useSortable({
      id: statusId, // Sử dụng statusId thay vì row.id
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <TableRow
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={isDragging ? "bg-muted/50" : ""}
    >
      <TableCell className="w-12 p-2">
        <div
          {...listeners}
          className="cursor-grab hover:cursor-grabbing p-1 rounded hover:bg-muted/50 transition-colors"
          onMouseDown={() => console.log("🖱️ Mouse down on drag handle", statusId)}
          onTouchStart={() => console.log("👆 Touch start on drag handle", statusId)}
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </div>
      </TableCell>
      {row.getVisibleCells().map((cell) => (
        <TableCell key={cell.id}>
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  );
}
