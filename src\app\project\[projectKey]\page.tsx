import { ActivityFeed } from "@/components/home/<USER>";
import { ChartBarLabel } from "@/components/home/<USER>";
import { ChartLineInteractive } from "@/components/home/<USER>";
import { ChartPieLabelList } from "@/components/home/<USER>";
import { IssueTracker } from "@/components/home/<USER>";

export default function Page() {
  return (
    <div className="flex flex-1 flex-col gap-4 p-4">
      <div>
        <ChartLineInteractive />
      </div>
      <div className="grid auto-rows-min gap-4 md:grid-cols-12">
        <div className="bg-muted/50 rounded-xl  col-span-8"><IssueTracker /></div>
        <div className="bg-muted/50 rounded-xl  col-span-4 gap-x-2">
          <ChartPieLabelList />
          <ChartBarLabel />
          <ActivityFeed />
        </div>
      </div>
      <div></div>
    </div>
  );
}
