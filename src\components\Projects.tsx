"use client"

import { useState } from "react"
import { Search, ExternalLink, Eye, Settings, Plus, ChevronDown, ChevronUp } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

const projects = [
  {
    id: 1,
    name: "E-commerce Platform",
    description: "Modern shopping experience",
    icon: "🛍️",
    status: "Active",
  },
  {
    id: 2,
    name: "Task Management App",
    description: "Productivity and collaboration",
    icon: "📋",
    status: "Active",
  },
  {
    id: 3,
    name: "Social Media Dashboard",
    description: "Analytics and insights",
    icon: "📊",
    status: "Draft",
  },
  {
    id: 4,
    name: "Learning Management System",
    description: "Educational platform",
    icon: "🎓",
    status: "Active",
  },
  {
    id: 5,
    name: "Real Estate Portal",
    description: "Property listings and search",
    icon: "🏠",
    status: "Active",
  },
  {
    id: 6,
    name: "Healthcare App",
    description: "Patient management system",
    icon: "🏥",
    status: "Draft",
  },
]

export default function Projects() {
  const [searchQuery, setSearchQuery] = useState("")
  const [hoveredProject, setHoveredProject] = useState<number | null>(null)
  const [isCollapsed, setIsCollapsed] = useState(false)

  const filteredProjects = projects.filter(
    (project) =>
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.description.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-2">Projects</h1>
                <p className="text-gray-600">Browse and manage your projects inventory</p>
              </div>
              <Button variant="ghost" size="sm" onClick={() => setIsCollapsed(!isCollapsed)} className="ml-4 p-2">
                {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            </div>

            <div className="flex items-center gap-4">
              {/* Search Bar - Moved to right */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-80 bg-white border-gray-200 focus:border-gray-300 focus:ring-gray-300"
                />
              </div>

              {/* Add Project Button */}
              <Button className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4" />
                Thêm dự án
              </Button>
            </div>
          </div>
        </div>

        {/* Projects List - Single Column with Collapse */}
        <div
          className={`transition-all duration-300 overflow-hidden ${
            isCollapsed ? "max-h-0 opacity-0" : "max-h-none opacity-100"
          }`}
        >
          <div className="space-y-4">
            {filteredProjects.map((project) => (
              <Card
                key={project.id}
                className="relative group cursor-pointer transition-all duration-200 hover:shadow-lg border-gray-200 bg-white"
                onMouseEnter={() => setHoveredProject(project.id)}
                onMouseLeave={() => setHoveredProject(null)}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="text-3xl">{project.icon}</div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-1 group-hover:text-blue-600 transition-colors">
                          {project.name}
                        </h3>
                        <p className="text-sm text-gray-600">{project.description}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div
                        className={`px-3 py-1 rounded-full text-xs font-medium ${
                          project.status === "Active" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {project.status}
                      </div>
                    </div>
                  </div>

                  {/* Hover Actions */}
                  <div
                    className={`absolute inset-0 bg-white/95 backdrop-blur-sm flex items-center justify-center gap-2 transition-opacity duration-200 ${
                      hoveredProject === project.id ? "opacity-100" : "opacity-0 pointer-events-none"
                    }`}
                  >
                    <Button
                      size="sm"
                      variant="outline"
                      className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-200 bg-transparent"
                      asChild
                    >
                      <Link href={`/projects/${project.id}`}>
                        <Eye className="h-4 w-4" />
                        View
                      </Link>
                    </Button>

                    <Button
                      size="sm"
                      variant="outline"
                      className="flex items-center gap-2 hover:bg-green-50 hover:border-green-200 bg-transparent"
                      asChild
                    >
                      <Link href={`/projects/${project.id}/edit`}>
                        <Settings className="h-4 w-4" />
                        Edit
                      </Link>
                    </Button>

                    <Button
                      size="sm"
                      variant="outline"
                      className="flex items-center gap-2 hover:bg-purple-50 hover:border-purple-200 bg-transparent"
                      asChild
                    >
                      <Link href={`/projects/${project.id}/preview`} target="_blank">
                        <ExternalLink className="h-4 w-4" />
                        Preview
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Empty State */}
          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
              <p className="text-gray-600">Try adjusting your search terms</p>
            </div>
          )}
        </div>

        {/* Collapsed State Info */}
        {isCollapsed && (
          <div className="text-center py-8 text-gray-500">
            <p>{filteredProjects.length} projects hidden</p>
          </div>
        )}
      </div>
    </div>
  )
}
