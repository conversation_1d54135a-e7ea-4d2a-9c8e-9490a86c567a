export type Category = {
  id: number;
  name: string;
  description?: string;
  order: number;
  projectId: number;
  projectKey: string;
  isActive: boolean;
  createdAt: Date;
  createdById: number;
  lastModifiedAt?: Date;
  lastModifiedById?: number;
  deletedAt?: Date;
  deletedById?: number;
};

export type CreateCategoryRequest = {
  name: string;
  description?: string;
  order: number;
  active: boolean;
};

export type UpdateCategoryRequest = {
  id: number;
  name: string;
  description?: string;
  active: boolean;
};
