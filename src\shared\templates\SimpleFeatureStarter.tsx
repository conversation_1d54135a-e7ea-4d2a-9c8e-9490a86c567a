// 🎯 COPY TEMPLATE NÀY ĐỂ BẮT ĐẦU FEATURE MỚI

// ============================================
// BƯỚC 1: ĐỊNH NGHĨA TYPES (Luôn bắt đầu từ đây)
// ============================================
export type MyItem = {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  isActive: boolean;
};

export type CreateMyItemRequest = {
  name: string;
  description?: string;
};

// ============================================
// BƯỚC 2: TẠO SERVICE VỚI MOCK DATA
// ============================================
const mockData: MyItem[] = [
  {
    id: "1",
    name: "Item đầu tiên",
    description: "Mô tả item đầu tiên",
    createdAt: new Date().toISOString(),
    isActive: true,
  },
  {
    id: "2", 
    name: "Item thứ hai",
    description: "Mô tả item thứ hai",
    createdAt: new Date().toISOString(),
    isActive: false,
  },
];

export const myItemService = {
  // Lấy tất cả items
  getAll: async (): Promise<MyItem[]> => {
    // Fake delay để giống real API
    await new Promise(resolve => setTimeout(resolve, 1000));
    return mockData;
  },

  // Tạo item mới
  create: async (data: CreateMyItemRequest): Promise<MyItem> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    const newItem: MyItem = {
      id: crypto.randomUUID(),
      ...data,
      createdAt: new Date().toISOString(),
      isActive: true,
    };
    mockData.push(newItem);
    return newItem;
  },

  // Xóa item
  delete: async (id: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const index = mockData.findIndex(item => item.id === id);
    if (index > -1) {
      mockData.splice(index, 1);
    }
  },
};

// ============================================
// BƯỚC 3: TẠO HOOKS (Business Logic)
// ============================================
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Hook để lấy danh sách
export const useMyItems = () => {
  return useQuery({
    queryKey: ["myItems"],
    queryFn: () => myItemService.getAll(),
  });
};

// Hook để tạo mới
export const useCreateMyItem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateMyItemRequest) => myItemService.create(data),
    onSuccess: () => {
      // Refresh danh sách sau khi tạo thành công
      queryClient.invalidateQueries({ queryKey: ["myItems"] });
      toast.success("Tạo thành công!");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi: ${error.message}`);
    },
  });
};

// Hook để xóa
export const useDeleteMyItem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => myItemService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["myItems"] });
      toast.success("Xóa thành công!");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi: ${error.message}`);
    },
  });
};

// ============================================
// BƯỚC 4: TẠO UI COMPONENTS (Đơn giản trước)
// ============================================
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// Component hiển thị danh sách
export function MyItemList() {
  const { data: items, isLoading, error } = useMyItems();
  const deleteMutation = useDeleteMyItem();

  // Loading state
  if (isLoading) {
    return <div className="p-4">Đang tải...</div>;
  }

  // Error state
  if (error) {
    return <div className="p-4 text-red-600">Có lỗi xảy ra: {error.message}</div>;
  }

  // Empty state
  if (!items || items.length === 0) {
    return <div className="p-4 text-gray-500">Chưa có item nào</div>;
  }

  return (
    <div className="space-y-4">
      {items.map((item) => (
        <Card key={item.id}>
          <CardHeader>
            <CardTitle className="flex justify-between items-center">
              <span>{item.name}</span>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => deleteMutation.mutate(item.id)}
                disabled={deleteMutation.isPending}
              >
                {deleteMutation.isPending ? "Đang xóa..." : "Xóa"}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">{item.description}</p>
            <p className="text-sm text-gray-400 mt-2">
              Tạo lúc: {new Date(item.createdAt).toLocaleString()}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Component form tạo mới
export function CreateMyItemForm() {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const createMutation = useCreateMyItem();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      toast.error("Tên không được để trống");
      return;
    }

    createMutation.mutate(
      { name: name.trim(), description: description.trim() || undefined },
      {
        onSuccess: () => {
          // Reset form sau khi tạo thành công
          setName("");
          setDescription("");
        },
      }
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tạo Item Mới</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium mb-1">
              Tên *
            </label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Nhập tên item..."
              required
            />
          </div>
          
          <div>
            <label htmlFor="description" className="block text-sm font-medium mb-1">
              Mô tả
            </label>
            <Input
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Nhập mô tả..."
            />
          </div>

          <Button 
            type="submit" 
            disabled={createMutation.isPending}
            className="w-full"
          >
            {createMutation.isPending ? "Đang tạo..." : "Tạo Item"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}

// ============================================
// BƯỚC 5: COMPONENT CHÍNH (Kết hợp tất cả)
// ============================================
export function MyFeaturePage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-2xl font-bold">Quản lý My Items</h1>
      
      {/* Form tạo mới */}
      <CreateMyItemForm />
      
      {/* Danh sách items */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Danh sách Items</h2>
        <MyItemList />
      </div>
    </div>
  );
}

// ============================================
// CÁCH SỬ DỤNG TEMPLATE NÀY:
// ============================================
/*
1. Copy file này
2. Đổi tên: MyItem → YourFeatureName
3. Thay đổi fields trong type theo nhu cầu
4. Chạy test xem có hoạt động không
5. Từ từ thêm features khác (edit, filter, etc.)

VÍ DỤ: Tạo feature "Quản lý Tags"
- MyItem → Tag
- myItemService → tagService  
- useMyItems → useTags
- CreateMyItemForm → CreateTagForm

REMEMBER: Bắt đầu đơn giản, mở rộng dần dần!
*/
