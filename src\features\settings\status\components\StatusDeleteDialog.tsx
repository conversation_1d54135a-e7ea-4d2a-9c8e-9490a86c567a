import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import { useDeleteStatus } from "@/features/settings/status/useDeleteStatus";
import { OctagonAlert, Trash } from "lucide-react";
import { useState } from "react";
import { Badge } from "../../../../components/ui/badge";
import { Status } from "@/shared/types/Status";

interface AlertDialogDestructiveProps {
  status: Status;
  onDelete: () => void;
}

export default function StatusDeleteDialog({
  status,
  onDelete,
}: AlertDialogDestructiveProps) {
  const deleteStatusMutation = useDeleteStatus();

  const [open, setOpen] = useState(false);
  const handleDelete = () => {
    deleteStatusMutation.mutate(status.id, {
      onSuccess: () => {
        setOpen(false);
        onDelete();
      },
    });
  };

  return (
    <AlertDialog open={open} onOpenChange={() => {}}>
      <AlertDialogTrigger asChild>
        <Button variant="outline" onClick={() => setOpen(true)}>
          <Trash className="text-red-500" />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader className="items-center">
          <AlertDialogTitle>
            <div className="mb-2 mx-auto flex h-14 w-14 items-center justify-center rounded-full bg-destructive/10">
              <OctagonAlert className="h-7 w-7 text-destructive" />
            </div>
            Bạn có chắc chắn không?
          </AlertDialogTitle>
          <AlertDialogDescription className="text-[15px] text-center">
            Hành động này không thể hoàn tác. Điều này sẽ xóa vĩnh viễn{" "}
            <Badge variant="outline" style={{ borderColor: status.color }}>{status.name}</Badge> ra khỏi hệ thống.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="mt-2 sm:justify-center">
          <AlertDialogCancel onClick={() => setOpen(false)}>
            Hủy
          </AlertDialogCancel>
          <AlertDialogAction
            className={buttonVariants({ variant: "destructive" })}
            onClick={handleDelete}
          >
            {deleteStatusMutation.isPending ? "Đang xóa..." : "Xóa"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
