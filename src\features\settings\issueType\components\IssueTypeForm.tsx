"use client";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useEffect } from "react";
import { IssueType } from "@/shared/types/IssueType";
import {
  issueTypeFormSchema,
  IssueTypeFormValues,
} from "../schemas/issueType-form.schema";
import { useUpdateIssueType } from "../useUpdateIssueType";
import { useCreateIssueType } from "../useCreateIssueType";
import {
  DEFAULT_CATEGORY_COLORS,
  ISSUE_TYPE_VALIDATION,
} from "@/shared/constants/issueTypeConstants";

export type IssueTypeFormProps = {
  mode?: "create" | "edit";
  issueType?: IssueType;
  onSuccess?: () => void;
  onCancel?: () => void;
};

export default function IssueTypeForm({
  mode,
  issueType,
  onSuccess,
  onCancel,
}: IssueTypeFormProps) {
  const form = useForm<IssueTypeFormValues>({
    resolver: zodResolver(issueTypeFormSchema),
    defaultValues: {
      name: "",
      description: "",
      color: "#3B82F6",
      order: 1,
      active: true,
    },
  });

  const createIssueTypeMutation = useCreateIssueType();
  const updateIssueTypeMutation = useUpdateIssueType();

  function onSubmit(values: IssueTypeFormValues) {
    console.log("Submitting form:", values);
    if (mode == "create") {
      createIssueTypeMutation.mutate(values, {
        onSuccess: () => {
          form.reset();
          onSuccess?.();
        },
      });
    }

    if (mode == "edit") {
      if (!issueType?.id) {
        throw new Error("IssueType id is required for update.");
      }
      updateIssueTypeMutation.mutate(
        { ...values, id: issueType.id },
        {
          onSuccess: () => {
            form.reset();
            onCancel?.();
          },
        }
      );
    }
  }

  useEffect(() => {
    if (issueType) {
      form.setValue("name", issueType.name);
      form.setValue("description", issueType.description || "");
      form.setValue("color", issueType.color);
      form.setValue("active", issueType.isActive);
    }
  }, [issueType, form]);

  return (
    <Form {...form}>
      <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
        {form.watch("name") && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <FormLabel className="text-sm font-medium text-gray-700 mb-2 block">
              Preview
            </FormLabel>
            <div
              className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold text-white"
              style={{ backgroundColor: form.watch("color") }}
            >
              {form.watch("name")}
            </div>
          </div>
        )}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tên loại issue</FormLabel>
              <FormControl>
                <Input
                  placeholder="Ví dụ: Bug, Feature, Task..."
                  {...field}
                  maxLength={ISSUE_TYPE_VALIDATION.NAME_MAX_LENGTH}
                  minLength={ISSUE_TYPE_VALIDATION.NAME_MIN_LENGTH}
                />
              </FormControl>
              <FormDescription>Tên ngắn gọn để phân loại issue</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mô tả</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Mô tả chi tiết về loại issue này..."
                  {...field}
                  maxLength={ISSUE_TYPE_VALIDATION.DESCRIPTION_MAX_LENGTH}
                />
              </FormControl>
              <FormDescription>
                Mô tả giúp người dùng hiểu rõ hơn về loại issue (tối đa 500 ký
                tự)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="color"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Màu sắc</FormLabel>
              <FormControl>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Input type="color" {...field} className="w-20 h-10 p-1" />
                    <span className="text-sm text-gray-500">
                      {field.value.toUpperCase()}
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {DEFAULT_CATEGORY_COLORS.map((color) => (
                      <button
                        key={color}
                        type="button"
                        className="w-8 h-8 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        style={{ backgroundColor: color }}
                        onClick={() => field.onChange(color)}
                      />
                    ))}
                  </div>
                </div>
              </FormControl>
              <FormDescription>
                Chọn màu để phân biệt với các loại issue khác
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Kích hoạt</FormLabel>
                <FormDescription>
                  Loại issue sẽ được hiển thị và có thể sử dụng
                </FormDescription>
              </div>
            </FormItem>
          )}
        />
        <Button type="submit">
          {mode === "create"
            ? createIssueTypeMutation.isPending
              ? "Đang lưu..."
              : "Lưu"
            : updateIssueTypeMutation.isPending
            ? "Đang cập nhật..."
            : "Cập nhật"}
        </Button>
      </form>
    </Form>
  );
}
