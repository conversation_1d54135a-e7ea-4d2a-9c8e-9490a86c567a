"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Copy,
  Eye,
  EyeOff,
  ArrowUp,
  ArrowDown,
  Users,
} from "lucide-react";
import { Category } from "@/shared/types/categoryTypes";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";

interface CategoryTableProps {
  categories: Category[];
  isLoading?: boolean;
  onEdit: (category: Category) => void;
  onDelete: (categoryId: string) => void;
  onDuplicate: (categoryId: string) => void;
  onToggleStatus: (categoryId: string, isActive: boolean) => void;
  onMoveUp: (categoryId: string) => void;
  onMoveDown: (categoryId: string) => void;
  selectedCategories?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
}

export default function CategoryTable({
  categories,
  isLoading = false,
  onEdit,
  onDelete,
  onDuplicate,
  onToggleStatus,
  onMoveUp,
  onMoveDown,
  selectedCategories = [],
  onSelectionChange,
}: CategoryTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);

  const handleDeleteClick = (category: Category) => {
    setCategoryToDelete(category);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (categoryToDelete) {
      onDelete(categoryToDelete.id);
      setDeleteDialogOpen(false);
      setCategoryToDelete(null);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? categories.map(cat => cat.id) : []);
    }
  };

  const handleSelectCategory = (categoryId: string, checked: boolean) => {
    if (onSelectionChange) {
      const newSelection = checked
        ? [...selectedCategories, categoryId]
        : selectedCategories.filter(id => id !== categoryId);
      onSelectionChange(newSelection);
    }
  };

  const isAllSelected = categories.length > 0 && selectedCategories.length === categories.length;
  const isIndeterminate = selectedCategories.length > 0 && selectedCategories.length < categories.length;

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-12 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (categories.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <div className="text-gray-500">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Chưa có category nào</h3>
            <p className="text-sm">Tạo category đầu tiên để bắt đầu phân loại issues.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Danh sách Categories ({categories.length})</span>
            {selectedCategories.length > 0 && (
              <Badge variant="secondary">
                Đã chọn {selectedCategories.length} category
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                {onSelectionChange && (
                  <TableHead className="w-12">
                    <input
                      type="checkbox"
                      checked={isAllSelected}
                      ref={(el) => {
                        if (el) el.indeterminate = isIndeterminate;
                      }}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="rounded border-gray-300"
                    />
                  </TableHead>
                )}
                <TableHead>Thứ tự</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Mô tả</TableHead>
                <TableHead>Issues</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead>Cập nhật</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {categories.map((category, index) => (
                <TableRow key={category.id}>
                  {onSelectionChange && (
                    <TableCell>
                      <input
                        type="checkbox"
                        checked={selectedCategories.includes(category.id)}
                        onChange={(e) => handleSelectCategory(category.id, e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    </TableCell>
                  )}
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <span className="text-sm text-gray-500">#{category.order}</span>
                      <div className="flex flex-col gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onMoveUp(category.id)}
                          disabled={index === 0}
                          className="h-4 w-4 p-0"
                        >
                          <ArrowUp className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onMoveDown(category.id)}
                          disabled={index === categories.length - 1}
                          className="h-4 w-4 p-0"
                        >
                          <ArrowDown className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Badge
                        style={{ backgroundColor: category.color }}
                        className="text-white"
                      >
                        {category.name}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-600">
                      {category.description || "Không có mô tả"}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {category.issueCount || 0} issues
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={category.isActive ? "default" : "secondary"}
                      className={category.isActive ? "bg-green-100 text-green-800" : ""}
                    >
                      {category.isActive ? "Hoạt động" : "Vô hiệu"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-500">
                      {formatDistanceToNow(new Date(category.updatedAt), {
                        addSuffix: true,
                        locale: vi,
                      })}
                    </span>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(category)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onDuplicate(category.id)}>
                          <Copy className="h-4 w-4 mr-2" />
                          Sao chép
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => onToggleStatus(category.id, !category.isActive)}
                        >
                          {category.isActive ? (
                            <>
                              <EyeOff className="h-4 w-4 mr-2" />
                              Vô hiệu hóa
                            </>
                          ) : (
                            <>
                              <Eye className="h-4 w-4 mr-2" />
                              Kích hoạt
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(category)}
                          className="text-red-600"
                          disabled={category.issueCount && category.issueCount > 0}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Xóa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa category</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa category "{categoryToDelete?.name}"? 
              Hành động này không thể hoàn tác.
              {categoryToDelete?.issueCount && categoryToDelete.issueCount > 0 && (
                <span className="block mt-2 text-red-600 font-medium">
                  Category này đang được sử dụng bởi {categoryToDelete.issueCount} issues.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
              disabled={categoryToDelete?.issueCount && categoryToDelete.issueCount > 0}
            >
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
