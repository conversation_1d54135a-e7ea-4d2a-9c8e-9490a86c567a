"use client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { issueTypeService } from "@/services/issueTypeService";
import { IssueType } from "@/shared/types/IssueType";
import { toast } from "sonner";

export const useReorderIssueType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (issueTypes: IssueType[]) => issueTypeService.reorderIssueTypes(issueTypes),
    onSuccess: () => {
      // Invalidate và refetch issue type list
      queryClient.invalidateQueries({ queryKey: ["issueTypes"] });

      toast.success("Đã cập nhật thứ tự loại vấn đề!");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi sắp xếp loại vấn đề: ${error.message}`);
    },
  });
};
