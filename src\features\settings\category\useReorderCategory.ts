import { useMutation, useQueryClient } from "@tanstack/react-query";
import { categoryService } from "@/services/categoryService";
import { ReorderCategoryValues } from "@/schema/categorySchema";
import { categoryQueryKeys } from "./useCategoryQuery";
import { toast } from "sonner";
import { Category } from "@/shared/types/categoryTypes";

export const useReorderCategories = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ReorderCategoryValues) => categoryService.reorderCategories(data),
    onMutate: async (newData) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: categoryQueryKeys.lists() });

      // Snapshot the previous value
      const previousCategories = queryClient.getQueryData(categoryQueryKeys.lists());

      // Optimistically update to the new value
      if (previousCategories) {
        const updatedCategories = [...previousCategories];

        // Update the order based on the new data
        newData.categories.forEach(({ id, order }) => {
          const categoryIndex = updatedCategories.findIndex(cat => cat.id === id);
          if (categoryIndex !== -1) {
            updatedCategories[categoryIndex] = {
              ...updatedCategories[categoryIndex],
              order,
            };
          }
        });

        // Sort by new order
        updatedCategories.sort((a, b) => a.order - b.order);

        queryClient.setQueryData(categoryQueryKeys.lists(), updatedCategories);
      }

      // Return a context object with the snapshotted value
      return { previousCategories };
    },
    onError: (err, newData, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousCategories) {
        queryClient.setQueryData(categoryQueryKeys.lists(), context.previousCategories);
      }
      toast.error(`Lỗi khi sắp xếp lại categories: ${err.message}`);
    },
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.lists() });
      toast.success("Đã sắp xếp lại categories thành công!");
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.lists() });
    },
  });
};

// Hook helper để tạo reorder data từ array categories
export const useCreateReorderData = () => {
  return (categories: Category[]): ReorderCategoryValues => {
    return {
      categories: categories.map((cat, index) => ({
        id: cat.id,
        order: index + 1,
      })),
    };
  };
};

// Hook để move category lên/xuống một vị trí
export const useMoveCategory = () => {
  const reorderMutation = useReorderCategories();
  const createReorderData = useCreateReorderData();
  const queryClient = useQueryClient();

  return {
    moveUp: (categoryId: string) => {
      const categories = queryClient.getQueryData<Category[]>(categoryQueryKeys.lists());
      if (!categories) return;

      const currentIndex = categories.findIndex(cat => cat.id === categoryId);
      if (currentIndex <= 0) return; // Already at top

      const newCategories = [...categories];
      [newCategories[currentIndex - 1], newCategories[currentIndex]] =
      [newCategories[currentIndex], newCategories[currentIndex - 1]];

      const reorderData = createReorderData(newCategories);
      reorderMutation.mutate(reorderData);
    },

    moveDown: (categoryId: string) => {
      const categories = queryClient.getQueryData<Category[]>(categoryQueryKeys.lists());
      if (!categories) return;

      const currentIndex = categories.findIndex(cat => cat.id === categoryId);
      if (currentIndex >= categories.length - 1) return; // Already at bottom

      const newCategories = [...categories];
      [newCategories[currentIndex], newCategories[currentIndex + 1]] =
      [newCategories[currentIndex + 1], newCategories[currentIndex]];

      const reorderData = createReorderData(newCategories);
      reorderMutation.mutate(reorderData);
    },

    isLoading: reorderMutation.isPending,
  };
};