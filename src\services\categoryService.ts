import {
  CreateCategoryFormValues,
  UpdateCategoryFormValues,
  ReorderCategoryValues,
} from "@/schema/categorySchema";
import {
  Category,
  CategoriesResponse,
  CategoryResponse,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  ReorderCategoryRequest,
  CategoryQueryParams,
  CategoryWithStats,
} from "@/shared/types/categoryTypes";

// Mock database với dữ liệu mẫu
let mockDB: Category[] = [
  {
    id: "1",
    name: "Backend",
    description: "Các vấn đề liên quan đến backend development",
    color: "#3B82F6",
    isActive: true,
    order: 1,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    issueCount: 15,
  },
  {
    id: "2",
    name: "Frontend",
    description: "<PERSON><PERSON>c vấn đề liên quan đến frontend development",
    color: "#10B981",
    isActive: true,
    order: 2,
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-02T00:00:00Z",
    issueCount: 8,
  },
  {
    id: "3",
    name: "Security",
    description: "Các vấn đề bảo mật",
    color: "#EF4444",
    isActive: true,
    order: 3,
    createdAt: "2024-01-03T00:00:00Z",
    updatedAt: "2024-01-03T00:00:00Z",
    issueCount: 3,
  },
  {
    id: "4",
    name: "Performance",
    description: "Các vấn đề về hiệu suất",
    color: "#F59E0B",
    isActive: false,
    order: 4,
    createdAt: "2024-01-04T00:00:00Z",
    updatedAt: "2024-01-04T00:00:00Z",
    issueCount: 2,
  },
];

// Helper function để simulate API delay
const simulateDelay = (ms: number = 800) =>
  new Promise((resolve) => setTimeout(resolve, ms));

// Helper function để filter và sort categories
const filterAndSortCategories = (
  categories: Category[],
  params?: CategoryQueryParams
): Category[] => {
  let filtered = [...categories];

  // Apply filters
  if (params?.filter) {
    const { search, isActive, projectId } = params.filter;

    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(
        (cat) =>
          cat.name.toLowerCase().includes(searchLower) ||
          cat.description?.toLowerCase().includes(searchLower)
      );
    }

    if (isActive !== undefined) {
      filtered = filtered.filter((cat) => cat.isActive === isActive);
    }

    if (projectId) {
      filtered = filtered.filter((cat) => cat.projectId === projectId);
    }
  }

  // Apply sorting
  if (params?.sort) {
    const { field, direction } = params.sort;
    filtered.sort((a, b) => {
      let aValue: any = a[field];
      let bValue: any = b[field];

      if (field === 'issueCount') {
        aValue = a.issueCount || 0;
        bValue = b.issueCount || 0;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (direction === 'desc') {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      }
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
    });
  } else {
    // Default sort by order
    filtered.sort((a, b) => a.order - b.order);
  }

  return filtered;
};

export const categoryService = {
  // Lấy danh sách categories với pagination và filter
  getCategories: async (params?: CategoryQueryParams): Promise<CategoriesResponse> => {
    await simulateDelay();

    const filtered = filterAndSortCategories(mockDB, params);
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = filtered.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: filtered.length,
      page,
      limit,
    };
  },

  // Lấy tất cả categories (không pagination)
  getAllCategories: async (): Promise<Category[]> => {
    await simulateDelay();
    return filterAndSortCategories(mockDB);
  },

  // Lấy chi tiết một category
  getCategory: async (id: string): Promise<CategoryResponse> => {
    await simulateDelay();
    const category = mockDB.find((cat) => cat.id === id);
    if (!category) {
      throw new Error(`Category với ID ${id} không tồn tại`);
    }
    return { data: category };
  },

  // Tạo category mới
  createCategory: async (input: CreateCategoryFormValues): Promise<CategoryResponse> => {
    await simulateDelay();

    // Validate tên category không trùng
    const existingCategory = mockDB.find(
      (cat) => cat.name.toLowerCase() === input.name.toLowerCase()
    );
    if (existingCategory) {
      throw new Error(`Category với tên "${input.name}" đã tồn tại`);
    }

    const maxOrder = Math.max(...mockDB.map((cat) => cat.order), 0);
    const newCategory: Category = {
      id: crypto.randomUUID(),
      name: input.name,
      description: input.description,
      color: input.color || "#6B7280",
      isActive: input.isActive ?? true,
      order: maxOrder + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      issueCount: 0,
    };

    mockDB.push(newCategory);
    return { data: newCategory };
  },

  // Cập nhật category
  updateCategory: async (input: UpdateCategoryFormValues): Promise<CategoryResponse> => {
    await simulateDelay();

    const categoryIndex = mockDB.findIndex((cat) => cat.id === input.id);
    if (categoryIndex === -1) {
      throw new Error(`Category với ID ${input.id} không tồn tại`);
    }

    // Validate tên category không trùng (nếu có thay đổi tên)
    if (input.name) {
      const existingCategory = mockDB.find(
        (cat) => cat.id !== input.id && cat.name.toLowerCase() === input.name.toLowerCase()
      );
      if (existingCategory) {
        throw new Error(`Category với tên "${input.name}" đã tồn tại`);
      }
    }

    const currentCategory = mockDB[categoryIndex];
    const updatedCategory: Category = {
      ...currentCategory,
      ...input,
      updatedAt: new Date().toISOString(),
    };

    mockDB[categoryIndex] = updatedCategory;
    return { data: updatedCategory };
  },

  // Xóa category
  deleteCategory: async (id: string): Promise<void> => {
    await simulateDelay();

    const categoryIndex = mockDB.findIndex((cat) => cat.id === id);
    if (categoryIndex === -1) {
      throw new Error(`Category với ID ${id} không tồn tại`);
    }

    const category = mockDB[categoryIndex];
    if (category.issueCount && category.issueCount > 0) {
      throw new Error(
        `Không thể xóa category "${category.name}" vì đang có ${category.issueCount} issue sử dụng`
      );
    }

    mockDB.splice(categoryIndex, 1);
  },

  // Sắp xếp lại thứ tự categories
  reorderCategories: async (input: ReorderCategoryValues): Promise<void> => {
    await simulateDelay();

    input.categories.forEach(({ id, order }) => {
      const category = mockDB.find((cat) => cat.id === id);
      if (category) {
        category.order = order;
        category.updatedAt = new Date().toISOString();
      }
    });
  },

  // Bulk delete categories
  bulkDeleteCategories: async (ids: string[]): Promise<void> => {
    await simulateDelay();

    const categoriesToDelete = mockDB.filter((cat) => ids.includes(cat.id));
    const categoriesWithIssues = categoriesToDelete.filter(
      (cat) => cat.issueCount && cat.issueCount > 0
    );

    if (categoriesWithIssues.length > 0) {
      const names = categoriesWithIssues.map((cat) => cat.name).join(", ");
      throw new Error(
        `Không thể xóa các category sau vì đang có issue sử dụng: ${names}`
      );
    }

    mockDB = mockDB.filter((cat) => !ids.includes(cat.id));
  },

  // Toggle active status
  toggleCategoryStatus: async (id: string, isActive: boolean): Promise<CategoryResponse> => {
    await simulateDelay();

    const categoryIndex = mockDB.findIndex((cat) => cat.id === id);
    if (categoryIndex === -1) {
      throw new Error(`Category với ID ${id} không tồn tại`);
    }

    mockDB[categoryIndex] = {
      ...mockDB[categoryIndex],
      isActive,
      updatedAt: new Date().toISOString(),
    };

    return { data: mockDB[categoryIndex] };
  },

  // Lấy categories với thống kê
  getCategoriesWithStats: async (): Promise<CategoryWithStats[]> => {
    await simulateDelay();

    return mockDB.map((cat) => ({
      ...cat,
      issueCount: cat.issueCount || 0,
      activeIssueCount: Math.floor((cat.issueCount || 0) * 0.7), // Mock data
      completedIssueCount: Math.floor((cat.issueCount || 0) * 0.3), // Mock data
    }));
  },

  // Duplicate category
  duplicateCategory: async (id: string): Promise<CategoryResponse> => {
    await simulateDelay();

    const originalCategory = mockDB.find((cat) => cat.id === id);
    if (!originalCategory) {
      throw new Error(`Category với ID ${id} không tồn tại`);
    }

    const maxOrder = Math.max(...mockDB.map((cat) => cat.order), 0);
    const duplicatedCategory: Category = {
      ...originalCategory,
      id: crypto.randomUUID(),
      name: `${originalCategory.name} (Copy)`,
      order: maxOrder + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      issueCount: 0,
    };

    mockDB.push(duplicatedCategory);
    return { data: duplicatedCategory };
  },
};
