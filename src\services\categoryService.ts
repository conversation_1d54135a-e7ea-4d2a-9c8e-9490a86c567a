import { CreateCategoryRequest, Category, UpdateCategoryRequest } from "@/shared/types/category";

const mockCategories: Category[] = [
  {
    id: 1,
    name: "Backend",
    description: "<PERSON><PERSON><PERSON> vấn đề liên quan đến backend development",
    order: 1,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
  {
    id: 2,
    name: "Frontend",
    description: "C<PERSON>c vấn đề liên quan đến frontend development",
    order: 2,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
];

export const categoryService = {
  getAll: async (): Promise<Category[]> => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return [...mockCategories];
  },

  createCategory: async (data: CreateCategoryRequest): Promise<Category> => {
    await new Promise((resolve) => setTimeout(resolve, 800));

    const existingCategory = mockCategories.find(
      (category) => category.name.toLowerCase() === data.name.toLowerCase()
    );
    if (existingCategory) {
      throw new Error(`Category với tên "${data.name}" đã tồn tại`);
    }

    const newCategory: Category = {
      id: Math.max(...mockCategories.map((c) => c.id), 0) + 1,
      name: data.name,
      description: data.description,
      order: data.order,
      projectId: 1,
      projectKey: "PRM",
      isActive: data.active,
      createdAt: new Date(),
      createdById: 1,
      lastModifiedAt: new Date(),
      lastModifiedById: 1,
    };

    mockCategories.push(newCategory);
    console.log("🚀 ~ newCategory:", newCategory);
    return newCategory;
  },

  updateCategory: async (data: UpdateCategoryRequest): Promise<Category> => {
    await new Promise((resolve) => setTimeout(resolve, 800));

    const existingIndex = mockCategories.findIndex((item) => item.id === data.id);
    if (existingIndex === -1) {
      throw new Error(`Category với ID ${data.id} không tồn tại`);
    }

    const duplicateName = mockCategories.find(
      (item) => item.id !== data.id && item.name.toLowerCase() === data.name.toLowerCase()
    );
    if (duplicateName) {
      throw new Error(`Category với tên "${data.name}" đã tồn tại`);
    }

    const updatedCategory: Category = {
      ...mockCategories[existingIndex],
      name: data.name,
      description: data.description,
      isActive: data.active,
      lastModifiedAt: new Date(),
      lastModifiedById: 1,
    };

    mockCategories[existingIndex] = updatedCategory;
    console.log("🚀 ~ updatedCategory:", updatedCategory);
    return updatedCategory;
  },

  reorderCategory: async (input: Category[]): Promise<void> => {
    mockCategories.sort((a, b) => {
      const aIndex = input.findIndex((i) => i.id === a.id);
      const bIndex = input.findIndex((i) => i.id === b.id);
      return (
        (aIndex === -1 ? Number.MAX_VALUE : aIndex) -
        (bIndex === -1 ? Number.MAX_VALUE : bIndex)
      );
    });
  },
};