export type Status = {
  id: number;
  name: string;
  description?: string;
  color: string;
  order: number;
  projectId: number;
  projectKey: string;
  isActive: boolean;
  createdAt: Date;
  createdById: number;
  lastModifiedAt?: Date;
  lastModifiedById?: number;
  deletedAt?: Date;
  deletedById?: number;
};

export type CreateStatusRequest = {
  name: string;
  description?: string;
  color: string;
  order: number;
  active: boolean;
};

export type UpdateStatusRequest = {
  id: number;
  name: string;
  description?: string;
  color: string;
  active: boolean;
};
