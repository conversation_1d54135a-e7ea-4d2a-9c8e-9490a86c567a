import { useMutation, useQueryClient } from "@tanstack/react-query";
import { categoryService } from "@/services/categoryService";
import { CreateCategoryFormValues } from "@/schema/categorySchema";
import { categoryQueryKeys } from "./useCategoryQuery";
import { toast } from "sonner";

export const useCreateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCategoryFormValues) => categoryService.createCategory(data),
    onSuccess: (response, variables) => {
      // Invalidate và refetch categories list
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.stats() });

      // Optionally set the new category in cache
      queryClient.setQueryData(
        categoryQueryKeys.detail(response.data.id),
        response
      );

      toast.success(`Category "${variables.name}" đã được tạo thành công!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi tạo category: ${error.message}`);
    },
  });
};
