"use client";

import { useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";

// Types
import { Category, CategoryFilter, CategorySort } from "@/shared/types/categoryTypes";
import { CreateCategoryFormValues, UpdateCategoryFormValues } from "@/schema/categorySchema";

// Hooks
import { useAllCategoriesQuery } from "./useCategoryQuery";
import { useCreateCategory } from "./useCreateCategory";
import { useUpdateCategory, useToggleCategoryStatus } from "./useUpdateCategory";
import { useDeleteCategory, useBulkDeleteCategories, useDuplicateCategory } from "./useDeleteCategory";
import { useMoveCategory } from "./useReorderCategory";

// Components
import CategoryForm from "./components/CategoryForm";
import CategoryTable from "./components/CategoryTable";
import CategoryFilters from "./components/CategoryFilters";

export function CategoryList() {
  // State
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [filter, setFilter] = useState<CategoryFilter>({});
  const [sort, setSort] = useState<CategorySort>({ field: "order", direction: "asc" });

  // Queries
  const { data: categories = [], isLoading, error } = useAllCategoriesQuery();

  // Mutations
  const createMutation = useCreateCategory();
  const updateMutation = useUpdateCategory();
  const deleteMutation = useDeleteCategory();
  const bulkDeleteMutation = useBulkDeleteCategories();
  const duplicateMutation = useDuplicateCategory();
  const toggleStatusMutation = useToggleCategoryStatus();
  const { moveUp, moveDown } = useMoveCategory();

  // Filter và sort categories
  const filteredAndSortedCategories = useMemo(() => {
    let filtered = [...categories];

    // Apply filters
    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      filtered = filtered.filter(
        (cat) =>
          cat.name.toLowerCase().includes(searchLower) ||
          cat.description?.toLowerCase().includes(searchLower)
      );
    }

    if (filter.isActive !== undefined) {
      filtered = filtered.filter((cat) => cat.isActive === filter.isActive);
    }

    if (filter.projectId) {
      filtered = filtered.filter((cat) => cat.projectId === filter.projectId);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sort.field];
      let bValue: any = b[sort.field];

      if (sort.field === 'issueCount') {
        aValue = a.issueCount || 0;
        bValue = b.issueCount || 0;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sort.direction === 'desc') {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      }
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
    });

    return filtered;
  }, [categories, filter, sort]);

  // Handlers
  const handleCreateCategory = async (data: CreateCategoryFormValues) => {
    try {
      await createMutation.mutateAsync(data);
      setIsFormOpen(false);
    } catch (error) {
      // Error handled in mutation
    }
  };

  const handleUpdateCategory = async (data: UpdateCategoryFormValues) => {
    try {
      await updateMutation.mutateAsync(data);
      setIsFormOpen(false);
      setEditingCategory(null);
    } catch (error) {
      // Error handled in mutation
    }
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setIsFormOpen(true);
  };

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      await deleteMutation.mutateAsync(categoryId);
      setSelectedCategories(prev => prev.filter(id => id !== categoryId));
    } catch (error) {
      // Error handled in mutation
    }
  };

  const handleBulkDelete = async () => {
    if (selectedCategories.length === 0) return;

    try {
      await bulkDeleteMutation.mutateAsync(selectedCategories);
      setSelectedCategories([]);
    } catch (error) {
      // Error handled in mutation
    }
  };

  const handleDuplicateCategory = async (categoryId: string) => {
    try {
      await duplicateMutation.mutateAsync(categoryId);
    } catch (error) {
      // Error handled in mutation
    }
  };

  const handleToggleStatus = async (categoryId: string, isActive: boolean) => {
    try {
      await toggleStatusMutation.mutateAsync({ id: categoryId, isActive });
    } catch (error) {
      // Error handled in mutation
    }
  };

  const handleResetFilters = () => {
    setFilter({});
    setSort({ field: "order", direction: "asc" });
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingCategory(null);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Có lỗi xảy ra khi tải danh sách categories.</p>
            <p className="text-sm mt-2">{error.message}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Quản lý Categories</h1>
          <p className="text-gray-600">
            Tạo và quản lý các categories để phân loại issues trong dự án.
          </p>
        </div>
        <div className="flex gap-2">
          {selectedCategories.length > 0 && (
            <Button
              variant="destructive"
              onClick={handleBulkDelete}
              disabled={bulkDeleteMutation.isPending}
              className="gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Xóa ({selectedCategories.length})
            </Button>
          )}
          <Button
            onClick={() => setIsFormOpen(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Tạo Category
          </Button>
        </div>
      </div>

      {/* Filters */}
      <CategoryFilters
        filter={filter}
        sort={sort}
        onFilterChange={setFilter}
        onSortChange={setSort}
        onReset={handleResetFilters}
        totalCount={categories.length}
        filteredCount={filteredAndSortedCategories.length}
      />

      {/* Table */}
      <CategoryTable
        categories={filteredAndSortedCategories}
        isLoading={isLoading}
        onEdit={handleEditCategory}
        onDelete={handleDeleteCategory}
        onDuplicate={handleDuplicateCategory}
        onToggleStatus={handleToggleStatus}
        onMoveUp={moveUp}
        onMoveDown={moveDown}
        selectedCategories={selectedCategories}
        onSelectionChange={setSelectedCategories}
      />

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={handleCloseForm}>
        <DialogContent className="">
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? "Chỉnh sửa Category" : "Tạo Category mới"}
            </DialogTitle>
          </DialogHeader>
          <CategoryForm
            category={editingCategory || undefined}
            onSubmit={editingCategory ? handleUpdateCategory : handleCreateCategory}
            onCancel={handleCloseForm}
            isLoading={createMutation.isPending || updateMutation.isPending}
            mode={editingCategory ? "edit" : "create"}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
