"use client";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useEffect } from "react";
import { Status } from "@/shared/types/status";
import {
  statusFormSchema,
  StatusFormValues,
} from "../schemas/statusFormSchema";
import { useUpdateStatus } from "../useUpdateStatus";
import { useCreateStatus } from "../useCreateStatus";
import {
  DEFAULT_CATEGORY_COLORS,
  STATUS_VALIDATION,
} from "@/shared/constants/statusContants";

export type statusFormProps = {
  mode?: "create" | "edit";
  status?: Status;
  onSuccess?: () => void;
  onCancel?: () => void;
};

export default function StatusForm({
  mode,
  status,
  onSuccess,
  onCancel,
}: statusFormProps) {
  const form = useForm<StatusFormValues>({
    resolver: zodResolver(statusFormSchema),
    defaultValues: {
      name: "",
      description: "",
      color: "#3B82F6",
      order: 1,
      active: true,
    },
  });

  const createStatusMutation = useCreateStatus();
  const updateStatusMutation = useUpdateStatus();

  function onSubmit(values: StatusFormValues) {
    console.log("Submitting form:", values);
    if (mode == "create") {
      createStatusMutation.mutate(values, {
        onSuccess: () => {
          form.reset();
          onSuccess?.();
        },
      });
    }

    if (mode == "edit") {
      if (!status?.id) {
        throw new Error("Status id is required for update.");
      }
      updateStatusMutation.mutate(
        { ...values, id: status.id },
        {
          onSuccess: () => {
            form.reset();
            onCancel?.();
          },
        }
      );
    }
  }

  useEffect(() => {
    if (status) {
      form.setValue("name", status.name);
      form.setValue("description", status.description || "");
      form.setValue("color", status.color);
      form.setValue("active", status.isActive);
    }
  }, [status, form]);

  return (
    <Form {...form}>
      <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
        {form.watch("name") && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <FormLabel className="text-sm font-medium text-gray-700 mb-2 block">
              Preview
            </FormLabel>
            <div
              className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold text-white"
              style={{ backgroundColor: form.watch("color") }}
            >
              {form.watch("name")}
            </div>
          </div>
        )}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tên loại issue</FormLabel>
              <FormControl>
                <Input
                  placeholder="Ví dụ: Bug, Feature, Task..."
                  {...field}
                  maxLength={STATUS_VALIDATION.NAME_MAX_LENGTH}
                  minLength={STATUS_VALIDATION.NAME_MIN_LENGTH}
                />
              </FormControl>
              <FormDescription>Tên ngắn gọn để phân loại issue</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mô tả</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Mô tả chi tiết về loại issue này..."
                  {...field}
                  maxLength={STATUS_VALIDATION.DESCRIPTION_MAX_LENGTH}
                />
              </FormControl>
              <FormDescription>
                Mô tả giúp người dùng hiểu rõ hơn về loại issue (tối đa 500 ký
                tự)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="color"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Màu sắc</FormLabel>
              <FormControl>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Input type="color" {...field} className="w-20 h-10 p-1" />
                    <span className="text-sm text-gray-500">
                      {field.value.toUpperCase()}
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {DEFAULT_CATEGORY_COLORS.map((color) => (
                      <button
                        key={color}
                        type="button"
                        className="w-8 h-8 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        style={{ backgroundColor: color }}
                        onClick={() => field.onChange(color)}
                      />
                    ))}
                  </div>
                </div>
              </FormControl>
              <FormDescription>
                Chọn màu để phân biệt với các loại issue khác
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Kích hoạt</FormLabel>
                <FormDescription>
                  Loại issue sẽ được hiển thị và có thể sử dụng
                </FormDescription>
              </div>
            </FormItem>
          )}
        />
        <Button type="submit">
          {mode === "create"
            ? createStatusMutation.isPending
              ? "Đang lưu..."
              : "Lưu"
            : updateStatusMutation.isPending
            ? "Đang cập nhật..."
            : "Cập nhật"}
        </Button>
      </form>
    </Form>
  );
}
