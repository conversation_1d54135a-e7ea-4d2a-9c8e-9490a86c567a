"use client";
import { z } from "zod";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";

const formSchema = z.object({
  name: z
    .string()
    .min(1, "Tên không được để trống")
    .max(50, "Tên không được quá 50 ký tự")
    .regex(
      /^[a-zA-Z0-9\s\u00C0-\u024F\u1E00-\u1EFF\-_\.]+$/,
      "Tên chỉ được chứa chữ cái, số, khoảng trắng và các ký tự đặc biệt: - _ ."
    ),
  description: z.string().optional(),
  color: z.string().min(1, "<PERSON><PERSON><PERSON> không được để trống"),
  order: z.number().int().min(1, "Thứ tự không được để trống"),
  active: z.boolean(),
});

export default function StatusForm({}) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      color: "#3B82F6",
      order: 1,
      active: true,
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values);
  }

  return (
    <>
      <Form {...form}>
        <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
          {
            form.watch("name")
          }
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tên loại issue</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Ví dụ: Bug, Feature, Task..."
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Tên ngắn gọn để phân loại issue
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mô tả</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Mô tả chi tiết về loại issue này..."
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Mô tả giúp người dùng hiểu rõ hơn về loại issue
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="color"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Màu sắc</FormLabel>
                <FormControl>
                  <div className="flex items-center gap-4">
                    <Input type="color" {...field} className="w-20 h-10 p-1" />
                    <span className="text-sm text-gray-500">
                      {field.value.toUpperCase()}
                    </span>
                  </div>
                </FormControl>
                <FormDescription>
                  Chọn màu để phân biệt với các loại issue khác
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="active"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Kích hoạt</FormLabel>
                  <FormDescription>
                    Loại issue sẽ được hiển thị và có thể sử dụng
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />
          <Button type="submit">Submit</Button>
        </form>
      </Form>
    </>
  );
}
