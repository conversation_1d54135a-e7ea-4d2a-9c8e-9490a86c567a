"use client";
import { z } from "zod";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";

const formSchema = z.object({
  name: z
    .string()
    .min(1, "Tên không được để trống")
    .max(50, "Tên không được quá 50 ký tự")
    .regex(
      /^[a-zA-Z0-9\s\u00C0-\u024F\u1E00-\u1EFF\-_\.]+$/,
      "Tên chỉ được chứa chữ cái, số, khoảng trắng và các ký tự đặc biệt: - _ ."
    ),
  description: z.string().optional(),
  color: z.string().min(1, "<PERSON><PERSON><PERSON> không được để trống"),
  order: z.number().int().min(1, "Thứ tự không được để trống"),
  active: z.boolean(),
});

export default function StatusForm({}) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      color: "#3B82F6",
      order: 1,
      active: true,
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values);
  }

  return (
    <>
      <Form {...form}>
        <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Username</FormLabel>
                <FormControl>
                  <Input placeholder="shadcn" {...field} />
                </FormControl>
                <FormDescription>
                  This is your public display name.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit">Submit</Button>
        </form>
      </Form>
    </>
  );
}
