import { issueTypeService } from "@/services/issueTypeService";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export const useDeleteIssueType = () => {

    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: number) => issueTypeService.deleteIssueType(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["issueTypes"] });
            toast.success("Đã xóa loại vấn đề thành công!");
        },
        onError: (error: Error) => {
            toast.error(`Lỗi khi xóa loại vấn đề: ${error.message}`);
        },
    });

};