"use client";

import { TrendingUp } from "lucide-react";
import { CartesianGrid, Line, LineChart, XAxis } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

export const description = "A multiple line chart";

const chartData = [
  { month: "January", frontend: 12, backend: 20, infra: 5, ui: 8 },
  { month: "February", frontend: 18, backend: 25, infra: 7, ui: 10 },
  { month: "March", frontend: 10, backend: 15, infra: 4, ui: 6 },
  { month: "April", frontend: 21, backend: 30, infra: 10, ui: 12 },
  { month: "May", frontend: 16, backend: 23, infra: 8, ui: 9 },
  { month: "June", frontend: 14, backend: 28, infra: 6, ui: 11 },
];

const chartConfig = {
  frontend: { label: "Frontend", color: "var(--chart-1)" },
  backend: { label: "Backend", color: "var(--chart-2)" },
  infra: { label: "Infrastructure", color: "var(--chart-3)" },
  ui: { label: "UI Design", color: "var(--chart-4)" },
} satisfies ChartConfig;

export function ChartLineMultiple() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Line Chart - Multiple</CardTitle>
        <CardDescription>January - June 2024</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <LineChart
            accessibilityLayer
            data={chartData}
            height={200}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
            <Line
              dataKey="frontend"
              stroke="var(--chart-1)"
              strokeWidth={2}
              dot={false}
            />
            <Line
              dataKey="backend"
              stroke="var(--chart-2)"
              strokeWidth={2}
              dot={false}
            />
            <Line
              dataKey="infra"
              stroke="var(--chart-3)"
              strokeWidth={2}
              dot={false}
            />
            <Line
              dataKey="ui"
              stroke="var(--chart-4)"
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Showing total visitors for the last 6 months
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
