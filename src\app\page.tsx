import AvatarDropdownMenuWithIcon from "@/components/AvatarDropdownMenuWithIcon";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { Progress } from "@/components/ui/progress";
import {
  BarChart3,
  Bell,
  Calendar,
  ChevronDown,
  GitPullRequest,
  HelpCircle,
  MoreHorizontal,
  Plus,
  Search,
  Star,
  MessageSquare,
  Eye,
  Grid3X3,
} from "lucide-react";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Top Navigation */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
                  <Grid3X3 className="w-4 h-4 text-primary-foreground" />
                </div>
                <span className="font-semibold text-lg">Datlog</span>
              </div>

              <NavigationMenu>
                <NavigationMenuList>
                  <NavigationMenuItem>
                    <NavigationMenuTrigger>Item One</NavigationMenuTrigger>
                    <NavigationMenuContent>
                      <NavigationMenuLink>Link</NavigationMenuLink>
                    </NavigationMenuContent>
                  </NavigationMenuItem>
                  <NavigationMenuItem>
                    <NavigationMenuTrigger>Item One</NavigationMenuTrigger>
                    <NavigationMenuContent>
                      <NavigationMenuLink>Link</NavigationMenuLink>
                    </NavigationMenuContent>
                  </NavigationMenuItem>
                  <NavigationMenuItem>
                    <NavigationMenuTrigger>Item One</NavigationMenuTrigger>
                    <NavigationMenuContent>
                      <NavigationMenuLink>Link</NavigationMenuLink>
                    </NavigationMenuContent>
                  </NavigationMenuItem>
                </NavigationMenuList>
              </NavigationMenu>
            </div>

            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search this Space"
                  className="pl-10 w-64 bg-gray-50 border-gray-200"
                />
              </div>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Bell className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <HelpCircle className="w-4 h-4" />
              </Button>
              <AvatarDropdownMenuWithIcon />
            </div>
          </div>
        </div>

        {/* Notification Banner */}
        <div className="bg-muted border-b px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                You can still invite 3 more users to your space.
              </span>
              <Link href="#" className="text-sm text-primary hover:underline">
                Invite more users
              </Link>
            </div>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              ×
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Projects Section */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ChevronDown className="w-4 h-4" />
                    <CardTitle className="text-lg">Projects</CardTitle>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Plus className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Search className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    {
                      name: "E-commerce Platform",
                      key: "ECOM",
                      progress: 75,
                      members: 8,
                      lastUpdate: "2 hours ago",
                      status: "active",
                    },
                    {
                      name: "Mobile App Redesign",
                      key: "MAR",
                      progress: 45,
                      members: 5,
                      lastUpdate: "1 day ago",
                      status: "active",
                    },
                    {
                      name: "Data Analytics Dashboard",
                      key: "DAD",
                      progress: 100,
                      members: 6,
                      lastUpdate: "3 days ago",
                      status: "completed",
                    },
                    {
                      name: "Customer Portal",
                      key: "CP",
                      progress: 20,
                      members: 4,
                      lastUpdate: "5 days ago",
                      status: "planning",
                    },
                  ].map((project) => (
                    <Link className="hover:shadow-2xl border-2 p-2 rounded-2xl" key={project.key} href={"/project/" + project.key} passHref>
                        <div>
                          <div className="flex items-center space-x-3 mb-3">
                            <div className="w-10 h-10 bg-muted rounded-md flex items-center justify-center border">
                              <span className="text-foreground font-semibold text-sm">
                                {project.key}
                              </span>
                            </div>
                            <div className="flex-1">
                              <h3 className="font-medium text-sm">
                                {project.name}
                              </h3>
                              <p className="text-xs text-muted-foreground">
                                {project.members} members • {project.lastUpdate}
                              </p>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between text-xs">
                              <span className="text-muted-foreground">
                                Progress
                              </span>
                              <span className="text-foreground">
                                {project.progress}%
                              </span>
                            </div>
                            <Progress
                              value={project.progress}
                              className="h-2"
                            />
                          </div>
                        </div>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* My Pull Requests */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ChevronDown className="w-4 h-4" />
                    <CardTitle className="text-lg">My Pull Requests</CardTitle>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Search className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">
                      Filters:
                    </span>
                    <Badge variant="secondary">Assigned to me</Badge>
                    <Badge variant="outline">Created by me</Badge>
                  </div>
                </div>

                <div className="border rounded-lg">
                  <div className="grid grid-cols-4 gap-4 p-3 bg-gray-50 text-sm font-medium text-gray-600 border-b">
                    <div>Key</div>
                    <div>Summary</div>
                    <div>Related Issue</div>
                    <div>Updated</div>
                  </div>
                  <div className="p-8 text-center text-gray-500">
                    <GitPullRequest className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                    <p>No pull requests to display</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* My Issues */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ChevronDown className="w-4 h-4" />
                    <CardTitle className="text-lg">My Issues</CardTitle>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <BarChart3 className="w-4 h-4" />
                      Gantt Chart
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Search className="w-4 h-4" />
                      Batch Update
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Calendar className="w-4 h-4" />
                      Calendar Import
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Search className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">
                      Filters:
                    </span>
                    <Badge
                      variant="secondary"
                      className="bg-green-100 text-green-800"
                    >
                      Assigned to me
                    </Badge>
                    <Badge variant="outline">Created by me</Badge>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <span>Due Today:</span>
                    <Badge variant="destructive">4</Badge>
                    <span>4 Days:</span>
                    <Badge variant="secondary">2</Badge>
                    <span>Due Today:</span>
                    <Badge variant="outline">0</Badge>
                    <span>Overdue:</span>
                    <Badge variant="destructive">1</Badge>
                  </div>
                </div>

                <div className="border rounded-lg">
                  <div className="grid grid-cols-6 gap-4 p-3 bg-gray-50 text-sm font-medium text-gray-600 border-b">
                    <div>Key</div>
                    <div>Subject</div>
                    <div>Priority</div>
                    <div>Status</div>
                    <div>Due</div>
                    <div>Assignee</div>
                  </div>

                  {[
                    {
                      key: "AAA-5",
                      subject: "UI",
                      priority: "high",
                      status: "In Progress",
                      due: "May 15",
                      assignee: "A",
                    },
                    {
                      key: "AAA-4",
                      subject: "zxcdsasd",
                      priority: "medium",
                      status: "To Do",
                      due: "May 16",
                      assignee: "A",
                    },
                    {
                      key: "AAA-1",
                      subject: "qw",
                      priority: "high",
                      status: "Open",
                      due: "",
                      assignee: "",
                    },
                    {
                      key: "AAA-23",
                      subject: "sd",
                      priority: "high",
                      status: "Open",
                      due: "",
                      assignee: "",
                    },
                  ].map((issue, index) => (
                    <div
                      key={index}
                      className="grid grid-cols-6 gap-4 p-3 border-b hover:bg-gray-50 text-sm"
                    >
                      <div className="text-blue-600 font-medium">
                        {issue.key}
                      </div>
                      <div>{issue.subject}</div>
                      <div>
                        <Badge
                          variant={
                            issue.priority === "high"
                              ? "destructive"
                              : "secondary"
                          }
                          className="text-xs"
                        >
                          {issue.priority}
                        </Badge>
                      </div>
                      <div>
                        <Badge variant="outline" className="text-xs">
                          {issue.status}
                        </Badge>
                      </div>
                      <div className="text-red-600">{issue.due}</div>
                      <div>
                        {issue.assignee && (
                          <Avatar className="w-6 h-6">
                            <AvatarFallback className="text-xs">
                              {issue.assignee}
                            </AvatarFallback>
                          </Avatar>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Backlog News */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <ChevronDown className="w-4 h-4" />
                  <CardTitle className="text-lg">Backlog News</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      title:
                        "How to use pulse polls to stop mistakes before they happen",
                      description:
                        "Use our latest mistakes — or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans, or send to humans to avoid common project management mistakes.",
                    },
                    {
                      title:
                        "How to build a continuous improvement culture that lasts",
                      description:
                        "If your team's stuck firefighting or frustrated by slow progress, a continuous improvement culture might be the shift you need.",
                    },
                    {
                      title:
                        "Remote team management: 23 strategies every leader should know",
                      description:
                        "Remote work has been around for years, but it therefore a flexible during the pandemic — and now it's a permanent fixture.",
                    },
                    {
                      title:
                        "Why bottlenecks happen and how smart teams avoid them",
                      description:
                        "What is a bottleneck — and why does it matter? In the race to deliver faster — whether that's code, deliveries, orders, or...",
                    },
                  ].map((article, index) => (
                    <div key={index} className="border-b pb-3 last:border-b-0">
                      <Link
                        href="#"
                        className="text-blue-600 hover:underline font-medium text-sm"
                      >
                        {article.title}
                      </Link>
                      <p className="text-xs text-gray-600 mt-1">
                        {article.description}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Recent Updates */}
          <div className="space-y-6">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Recent Updates</CardTitle>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" className="text-xs">
                      Filter All
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Eye className="w-4 h-4" />
                      View Options
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      user: "Leidat",
                      action: "posted a comment",
                      project: "AAA-5 #4",
                      description: "Merged AAA/CRX cq2",
                      time: "2 days ago",
                      type: "comment",
                    },
                    {
                      user: "Leidat",
                      action: "to develop at CX",
                      project: "INN [AAA] - CX",
                      description:
                        "bb1f8b5aa - Merge pull request #1 cq2 into develop",
                      time: "2 days ago",
                      type: "develop",
                    },
                    {
                      user: "Leidat",
                      action: "the pull request",
                      project: "AAA/CRX cq2",
                      description: "1 Status: Merged",
                      time: "2 days ago",
                      type: "pull_request",
                    },
                    {
                      user: "Leidat",
                      action: "to cq2 at CX",
                      project: "INN [AAA] - CX",
                      description:
                        "bb1f8b5aa branch: develop of https://github.com/backlog.com/git/AAA/CRX into cq2",
                      time: "2 days ago",
                      type: "develop",
                    },
                    {
                      user: "Leidat",
                      action: "posted a comment",
                      project: "AAA-5 #4",
                      description: "Add AAA/CRX cq2",
                      time: "2 days ago",
                      type: "comment",
                    },
                    {
                      user: "Leidat",
                      action: "added a new git branch",
                      project: "AAA/CRX cq2",
                      description: "bb pull request cq2 indent cq2",
                      time: "2 days ago",
                      type: "git_branch",
                    },
                  ].map((update, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <Avatar className="w-8 h-8 flex-shrink-0">
                        <AvatarFallback className="bg-muted text-foreground text-xs">
                          {update.user.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-medium text-sm">
                            {update.user}
                          </span>
                          <span className="text-sm text-gray-600">
                            {update.action}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {update.type === "comment"
                              ? "comment"
                              : update.type === "develop"
                              ? "develop"
                              : update.type === "pull_request"
                              ? "pull request"
                              : "git branch"}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-800 mb-1">
                          {update.project}
                        </p>
                        <p className="text-xs text-gray-600 mb-2">
                          {update.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">
                            {update.time}
                          </span>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                            >
                              <MessageSquare className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                            >
                              <Star className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
