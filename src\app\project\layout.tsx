import { AppSidebar } from "@/components/app-sidebar";
import AvatarDropdownMenuWithIcon from "@/components/AvatarDropdownMenuWithIcon";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON>barInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Bell, HelpCircle, MoreHorizontal, Search } from "lucide-react";

export default function Page({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <div className="h-screen flex flex-col">
          <header className="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 bg-accent">
            <div className="flex items-center gap-2 px-5 w-full justify-between">
              <SidebarTrigger className="-ml-1" />
           
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search this Space"
                    className="pl-10 w-64 bg-gray-50 border-gray-200"
                  />
                </div>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Bell className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <HelpCircle className="w-4 h-4" />
                </Button>
                <AvatarDropdownMenuWithIcon />
              </div>
            </div>
          </header>
          <main className="flex-1 overflow-y-auto"> {children}</main>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
