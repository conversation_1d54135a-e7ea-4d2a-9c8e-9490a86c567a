"use client";

import type React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

const settingsNavItems = [
  {
    title: "General",
    href: "./general",
  },
  {
    title: "Status",
    href: "./status",
  },
  {
    title: "Issue Types",
    href: "./types",
  },
  {
    title: "Categories",
    href: "./categories",
  },
  {
    title: "Versions",
    href: "./versions",
  },
  {
    title: "Milestones",
    href: "./milestones",
  },
  {
    title: "Priorities",
    href: "./priorities",
  },
  {
    title: "Authorations",
    href: "./authorations",
  },
];

function SettingsNav({ className }: { className?: string }) {
  const pathname = usePathname();

  return (
    <nav
      className={cn(
        "flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1",
        className
      )}
    >
      {settingsNavItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={cn(
            "justify-start px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-muted whitespace-nowrap",
            pathname === item.href
              ? "bg-muted text-foreground"
              : "text-muted-foreground hover:text-foreground"
          )}
        >
          {item.title}
        </Link>
      ))}
    </nav>
  );
}

export default function SettingsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="container mx-auto px-4 py-4 md:py-8">
      <div className="space-y-4 md:space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl md:text-2xl font-bold">Settings</h1>
            <p className="text-sm text-muted-foreground hidden sm:block">
              Manage your project settings.
            </p>
          </div>
        </div>

        <div className="flex flex-col space-y-6 lg:flex-row lg:space-x-8 xl:space-x-12 lg:space-y-0">
          {/* Desktop sidebar */}
          <aside className="hidden lg:block lg:w-48 xl:w-56 flex-shrink-0">
            <SettingsNav />
          </aside>

          {/* Mobile horizontal nav */}
          <div className="lg:hidden">
            <div className="overflow-x-auto pb-2">
              <SettingsNav className="flex-nowrap min-w-max" />
            </div>
          </div>

          {/* Main content */}
          <div className="flex-1 min-w-0">
            <div className="w-full">{children}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
