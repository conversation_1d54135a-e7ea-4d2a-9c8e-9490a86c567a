import { IssueType } from "@/shared/types/IssueType";
import { useIssueType } from "../useIssueTypeQuery";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  createColumnHelper,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { SortableContext } from "@dnd-kit/sortable";
import { IssueTypeDraggableRow } from "./IssueTypeDraggableRow";
import {
  restrictToVerticalAxis,
  restrictToParentElement,
} from "@dnd-kit/modifiers";
import { useReorderIssueType } from "../useReorderIssueType";

const columnHelper = createColumnHelper<IssueType>();

const columns = [
  columnHelper.accessor("name", {
    header: "Tên",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <div
          className="h-4 w-4 rounded-full"
          style={{ backgroundColor: row.original.color }}
        />
        {row.getValue("name")}
      </div>
    ),
  }),
  columnHelper.accessor("description", {
    header: "Mô tả",
  }),
  columnHelper.accessor("color", {
    header: "Màu sắc",
  }),
  columnHelper.accessor("order", {
    header: "Thứ tự",
  }),
  columnHelper.accessor("isActive", {
    header: "Hoạt động",
    cell: ({ row }) => (
      <div className="capitalize">
        {row.getValue("isActive") ? "Có" : "Không"}
      </div>
    ),
  }),
  // columnHelper.accessor("createdAt", {
  //   header: "Ngày tạo",
  // }),
  // columnHelper.accessor("lastModifiedAt", {
  //   header: "Ngày cập nhật",
  // }),
  // columnHelper.accessor("id", {
  //   header: "Thao tác",
  //   cell(props) {
  //     return (
  //       <div className="flex gap-2">
  //         <StatusDialog
  //           mode="edit"
  //           trigger={
  //             <Button variant="outline">
  //               <Edit2Icon />
  //             </Button>
  //           }
  //           status={props.row.original}
  //         />
  //         <AlertDialogDestructive
  //           status={props.row.original}
  //           onDelete={() => {}}
  //         />
  //       </div>
  //     );
  //   },
  // }),
];


export function IssueTypeList() {
  const { data: issueTypes, isLoading, error } = useIssueType();
  const reorderMutation = useReorderIssueType();

   const table = useReactTable({
     data: issueTypes || [],
     columns,
     getCoreRowModel: getCoreRowModel(),
   });
 
   if (isLoading) {
     return <div>Loading...</div>;
   }
 
   if (error) {
     return <div>Error: {error.message}</div>;
   }
 
   function handleDragEnd(event: DragEndEvent) {
     const { active, over } = event;
 
     console.log("🎯 Drag event:", {
       activeId: active?.id,
       overId: over?.id,
       activeType: typeof active?.id,
       overType: typeof over?.id,
     });
 
     if (!over) {
       console.log("❌ No over target");
       return;
     }
 
     // Convert string IDs back to numbers for comparison
     const activeId = parseInt(active.id as string);
     const overId = parseInt(over.id as string);
 
     const oldIndex = issueTypes?.findIndex((i) => i.id === activeId);
     const newIndex = issueTypes?.findIndex((i) => i.id === overId);
 
     if (
       oldIndex === undefined ||
       newIndex === undefined ||
       oldIndex === -1 ||
       newIndex === -1
     ) {
       return;
     }
 
     if (oldIndex === newIndex) {
       return;
     }
 
     const newIssueTypes = [...issueTypes!];
     const [removed] = newIssueTypes.splice(oldIndex, 1);
     newIssueTypes.splice(newIndex, 0, removed);
 
     reorderMutation.mutate(newIssueTypes);
   }

  return (
    <DndContext
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis, restrictToParentElement]}
    >
      <SortableContext items={issueTypes?.map((i) => i.id.toString()) ?? []}>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                <TableHead className="w-12">Kéo</TableHead>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody
            className={reorderMutation.isPending ? "opacity-50 disabled" : ""}
          >
            {table.getRowModel().rows.map((row) => (
              <IssueTypeDraggableRow
                key={row.original.id}
                row={row}
                issueTypeId={row.original.id.toString()}
              />
            ))}
          </TableBody>
        </Table>
        <div className="mt-4 space-y-2">
          <div>Kéo thả để sắp xếp thứ tự</div>
        </div>
      </SortableContext>
    </DndContext>
  );
}
