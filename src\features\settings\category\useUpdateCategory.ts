import { useMutation, useQueryClient } from "@tanstack/react-query";
import { categoryService } from "@/services/categoryService";
import { UpdateCategoryFormValues } from "@/schema/categorySchema";
import { categoryQueryKeys } from "./useCategoryQuery";
import { toast } from "sonner";

export const useUpdateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateCategoryFormValues) => categoryService.updateCategory(data),
    onSuccess: (response, variables) => {
      // Invalidate và refetch categories list
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.stats() });

      // Update specific category in cache
      queryClient.setQueryData(
        categoryQueryKeys.detail(variables.id!),
        response
      );

      toast.success(`Category "${response.data.name}" đã được cập nhật thành công!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi cập nhật category: ${error.message}`);
    },
  });
};

// Hook riêng để toggle status
export const useToggleCategoryStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, isActive }: { id: string; isActive: boolean }) =>
      categoryService.toggleCategoryStatus(id, isActive),
    onSuccess: (response, variables) => {
      // Invalidate và refetch categories list
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.stats() });

      // Update specific category in cache
      queryClient.setQueryData(
        categoryQueryKeys.detail(variables.id),
        response
      );

      const status = variables.isActive ? "kích hoạt" : "vô hiệu hóa";
      toast.success(`Category "${response.data.name}" đã được ${status}!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi thay đổi trạng thái category: ${error.message}`);
    },
  });
};