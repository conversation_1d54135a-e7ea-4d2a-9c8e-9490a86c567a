import { useMutation, useQueryClient } from "@tanstack/react-query";
import { statusService } from "@/services/statusService";
import { toast } from "sonner";
import { Status } from "@/shared/types/status";

export const useReorderStatus = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: Status[]) => statusService.reorderStatus(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["useStatus"] });
      toast.success("Đã sắp xếp lại status thành công!");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi sắp xếp lại status: ${error.message}`);
    },
  });
};
