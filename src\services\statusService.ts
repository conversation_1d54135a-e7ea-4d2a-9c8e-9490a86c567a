import { Status } from "@/shared/types/status";

const mockStatuses: Status[] = [{
  id: 1,
  name: "Open",
  description: "Issue is newly created and not yet started",
  color: "#6366F1",
  order: 1,
  projectId: 1,
  projectKey: "PRM",
  isDeleted: false,
  createdAt: new Date("2024-01-01"),
  createdById: 1,
  lastModifiedAt: new Date("2024-01-01"),
  lastModifiedById: 1
}, {
  id: 2,
  name: "In Progress",
  description: "Issue is currently being worked on",
  color: "#3B82F6",
  order: 2,
  projectId: 1,
  projectKey: "PRM",
  isDeleted: false,
  createdAt: new Date("2024-01-01"),
  createdById: 1,
  lastModifiedAt: new Date("2024-01-01"),
  lastModifiedById: 1
}, {
  id: 3,
  name: "Done",
  description: "Issue has been completed",
  color: "#10B981",
  order: 3,
  projectId: 1,
  projectKey: "PRM",
  isDeleted: false,
  createdAt: new Date("2024-01-01"),
  createdById: 1,
  lastModifiedAt: new Date("2024-01-01"),
  lastModifiedById: 1
}]


export const statusService = {
  getAll: async (): Promise<Status[]> => {
    await new Promise(resolve => setTimeout(resolve, 500)); // Giả delay API
    return [...mockStatuses]; // Trả về danh sách status (giống như gọi API)
  },
};
