import { Status, CreateStatusRequest } from "@/shared/types/status";

const mockStatuses: Status[] = [
  {
    id: 1,
    name: "Open",
    description: "Issue is newly created and not yet started",
    color: "#6366F1",
    order: 1,
    projectId: 1,
    projectKey: "PRM",
    isDeleted: false,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
  {
    id: 2,
    name: "In Progress",
    description: "Issue is currently being worked on",
    color: "#3B82F6",
    order: 2,
    projectId: 1,
    projectKey: "PRM",
    isDeleted: false,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
  {
    id: 3,
    name: "Done",
    description: "Issue has been completed",
    color: "#10B981",
    order: 3,
    projectId: 1,
    projectKey: "PRM",
    isDeleted: false,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
];

export const statusService = {
  getAll: async (): Promise<Status[]> => {
    await new Promise((resolve) => setTimeout(resolve, 500)); // Giả delay API
    return [...mockStatuses]; // Trả về danh sách status (giống như gọi API)
  },
  createStatus: async (data: CreateStatusRequest): Promise<Status> => {
    await new Promise((resolve) => setTimeout(resolve, 800));

    // Validate tên không trùng
    const existingStatus = mockStatuses.find(
      (status) => status.name.toLowerCase() === data.name.toLowerCase()
    );
    if (existingStatus) {
      throw new Error(`Status với tên "${data.name}" đã tồn tại`);
    }

    // Tạo status mới với ID và các field bắt buộc
    const newStatus: Status = {
      id: Math.max(...mockStatuses.map((s) => s.id), 0) + 1,
      name: data.name,
      description: data.description,
      color: data.color,
      order: data.order,
      projectId: 1, // Mock project ID
      projectKey: "PRM", // Mock project key
      isDeleted: data.active,
      createdAt: new Date(),
      createdById: 1, // Mock user ID
      lastModifiedAt: new Date(),
      lastModifiedById: 1,
    };

    mockStatuses.push(newStatus);
    return newStatus;
  },

  reorderStatus: async (input: Status[]): Promise<void> => {
    mockStatuses.sort((a, b) => {
      const aIndex = input.findIndex((i) => i.id === a.id);
      const bIndex = input.findIndex((i) => i.id === b.id);
      return (
        (aIndex === -1 ? Number.MAX_VALUE : aIndex) -
        (bIndex === -1 ? Number.MAX_VALUE : bIndex)
      );
    });
  },
};
