import {
  Status,
  CreateStatusRequest,
  UpdateStatusRequest,
} from "@/shared/types/status";

const mockStatuses: Status[] = [
  {
    id: 1,
    name: "Open",
    description: "Issue is newly created and not yet started",
    color: "#6366F1",
    order: 1,
    projectId: 1,
    projectKey: "PRM",
    isDeleted: false,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
  {
    id: 2,
    name: "In Progress",
    description: "Issue is currently being worked on",
    color: "#3B82F6",
    order: 2,
    projectId: 1,
    projectKey: "PRM",
    isActive: false,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
  {
    id: 3,
    name: "Done",
    description: "Issue has been completed",
    color: "#10B981",
    order: 3,
    projectId: 1,
    projectKey: "PRM",
    isActive: false,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
];

export const statusService = {
  getAll: async (): Promise<Status[]> => {
    await new Promise((resolve) => setTimeout(resolve, 500)); // Giả delay API
    return [...mockStatuses]; // Trả về danh sách status (giống như gọi API)
  },
  createStatus: async (data: CreateStatusRequest): Promise<Status> => {
    await new Promise((resolve) => setTimeout(resolve, 800));

    // Validate tên không trùng
    const existingStatus = mockStatuses.find(
      (status) => status.name.toLowerCase() === data.name.toLowerCase()
    );
    if (existingStatus) {
      throw new Error(`Status với tên "${data.name}" đã tồn tại`);
    }

    
    // Tạo status mới với ID và các field bắt buộc
    const newStatus: Status = {
      id: Math.max(...mockStatuses.map((s) => s.id), 0) + 1,
      name: data.name,
      description: data.description,
      color: data.color,
      order: data.order,
      projectId: 1, // Mock project ID
      projectKey: "PRM", // Mock project key
      isActive: data.active,
      createdAt: new Date(),
      createdById: 1, // Mock user ID
      lastModifiedAt: new Date(),
      lastModifiedById: 1,
    };
    console.log("🚀 ~ newStatus:", newStatus)

    mockStatuses.push(newStatus);
    return newStatus;
  },

  updateStatus: async (data: UpdateStatusRequest): Promise<Status> => {
    await new Promise((resolve) => setTimeout(resolve, 800));

    const existingStatus = mockStatuses.find((status) => status.id === data.id);
    if (!existingStatus) {
      throw new Error(`Status với ID ${data.id} không tồn tại`);
    }

    const existingStatusName = mockStatuses.find(
      (status) => status.name.toLowerCase() === data.name.toLowerCase()
    );
    if (existingStatusName && existingStatusName.id !== data.id) {
      throw new Error(`Status với tên "${data.name}" đã tồn tại`);
    }

    const updatedStatus: Status = {
      ...existingStatus,
      ...data,
      lastModifiedAt: new Date(),
      lastModifiedById: 1,
    };

    mockStatuses[mockStatuses.indexOf(existingStatus)] = updatedStatus;
    return updatedStatus;
  },

  deleteStatus: async (id: number): Promise<void> => {
    await new Promise((resolve) => setTimeout(resolve, 300));
    const statusIndex = mockStatuses.findIndex((status) => status.id === id);
    if (statusIndex === -1) {
      throw new Error(`Status với ID ${id} không tồn tại`);
    }
    mockStatuses.splice(statusIndex, 1);
  },

  reorderStatus: async (input: Status[]): Promise<void> => {
    mockStatuses.sort((a, b) => {
      const aIndex = input.findIndex((i) => i.id === a.id);
      const bIndex = input.findIndex((i) => i.id === b.id);
      return (
        (aIndex === -1 ? Number.MAX_VALUE : aIndex) -
        (bIndex === -1 ? Number.MAX_VALUE : bIndex)
      );
    });
  },
};
