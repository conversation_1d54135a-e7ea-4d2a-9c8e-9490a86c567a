"use client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useStatus } from "../useStatusQuery";
import {
  useReactTable,
  getCoreRowModel,
  createColumnHelper,
  flexRender,
} from "@tanstack/react-table";
import { Status } from "@/shared/types/status";
import {
  DndContext,
  DragEndEvent,
} from "@dnd-kit/core";
import { DraggableRow } from "./DraggableRow";
import {
  restrictToVerticalAxis,
  restrictToParentElement,
} from "@dnd-kit/modifiers";
import { SortableContext } from "@dnd-kit/sortable";
const columnHelper = createColumnHelper<Status>();

const columns = [
  columnHelper.accessor("name", {
    header: "Tên",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <div
          className="h-4 w-4 rounded-full"
          style={{ backgroundColor: row.original.color }}
        />
        {row.getValue("name")}
      </div>
    ),
  }),
  columnHelper.accessor("description", {
    header: "<PERSON>ô tả",
  }),
  columnHelper.accessor("color", {
    header: "Màu sắc",
  }),
  columnHelper.accessor("order", {
    header: "Thứ tự",
  }),
  columnHelper.accessor("isDeleted", {
    header: "Hoạt động",
    cell: ({ row }) => (
      <div className="capitalize">
        {row.getValue("isDeleted") ? "Không" : "Có"}
      </div>
    ),
  }),
];

export function StatusList() {
  const { data: statuses, isLoading, error } = useStatus();
  const table = useReactTable({
    data: statuses || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event;

    if (!active || !over || active.id === over.id) return;
    console.log("Reorder từ", active.id, "->", over.id);
    // Cập nhật lại danh sách đã reorder

    // TODO: cập nhật state để re-render table
    // Nếu data đến từ react-query, bạn có thể dùng setQueryData
    // Hoặc nếu bạn dùng useState cho danh sách thì update state
  }

  return (
    <DndContext
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis, restrictToParentElement]}
    >
      <SortableContext items={statuses?.map((i) => i.id) ?? []}>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <DraggableRow key={row.id} row={row} />
            ))}
          </TableBody>
        </Table>
        <div className="mt-4">Kéo thả để sắp xếp thứ tự</div>
      </SortableContext>
    </DndContext>
  );
}
