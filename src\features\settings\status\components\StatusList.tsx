"use client";
import { useStatus } from "../useStatusQuery";

export function StatusList() {
  const { data: statuses, isLoading, error } = useStatus();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div>
      {statuses?.map((status) => (
        <div key={status.id}>{status.name}</div>
      ))}
    </div>
  );
}
