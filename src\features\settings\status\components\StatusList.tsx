"use client";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useStatus } from "../useStatusQuery";
import {
  useReactTable,
  getCoreRowModel,
  createColumnHelper,
  flexRender,
} from "@tanstack/react-table";
import { Status } from "@/shared/types/status";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { DraggableRow } from "./DraggableRow";
import {
  restrictToVerticalAxis,
  restrictToParentElement,
} from "@dnd-kit/modifiers";
import { SortableContext } from "@dnd-kit/sortable";
import { useReorderStatus } from "../useReorderStatus";
const columnHelper = createColumnHelper<Status>();

const columns = [
  columnHelper.accessor("name", {
    header: "Tên",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <div
          className="h-4 w-4 rounded-full"
          style={{ backgroundColor: row.original.color }}
        />
        {row.getValue("name")}
      </div>
    ),
  }),
  columnHelper.accessor("description", {
    header: "Mô tả",
  }),
  columnHelper.accessor("color", {
    header: "Màu sắc",
  }),
  columnHelper.accessor("order", {
    header: "Thứ tự",
  }),
  columnHelper.accessor("isDeleted", {
    header: "Hoạt động",
    cell: ({ row }) => (
      <div className="capitalize">
        {row.getValue("isDeleted") ? "Không" : "Có"}
      </div>
    ),
  }),
];

export function StatusList() {
  const { data: statuses, isLoading, error } = useStatus();
  const reorderMutation = useReorderStatus();

  const table = useReactTable({
    data: statuses || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }



  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event;

    console.log("🎯 Drag event:", {
      activeId: active?.id,
      overId: over?.id,
      activeType: typeof active?.id,
      overType: typeof over?.id,
    });

    if (!over) {
      console.log("❌ No over target");
      return;
    }

    // Convert string IDs back to numbers for comparison
    const activeId = parseInt(active.id as string);
    const overId = parseInt(over.id as string);

    const oldIndex = statuses?.findIndex((i) => i.id === activeId);
    const newIndex = statuses?.findIndex((i) => i.id === overId);

    console.log("📍 Indexes:", { oldIndex, newIndex, activeId, overId });

    if (
      oldIndex === undefined ||
      newIndex === undefined ||
      oldIndex === -1 ||
      newIndex === -1
    ) {
      console.log("❌ Invalid indexes");
      return;
    }

    if (oldIndex === newIndex) {
      console.log("❌ Same position");
      return;
    }

    console.log("✅ Reordering...");
    const newStatuses = [...statuses!];
    const [removed] = newStatuses.splice(oldIndex, 1);
    newStatuses.splice(newIndex, 0, removed);

    reorderMutation.mutate(newStatuses);
  }

  return (
    <DndContext
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis, restrictToParentElement]}
    >
      <SortableContext items={statuses?.map((i) => i.id.toString()) ?? []}>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                <TableHead className="w-12">Kéo</TableHead>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody
            className={reorderMutation.isPending ? "opacity-50 disabled" : ""}
          >
            {table.getRowModel().rows.map((row) => (
              <DraggableRow
                key={row.original.id}
                row={row}
                statusId={row.original.id.toString()}
              />
            ))}
          </TableBody>
        </Table>
        <div className="mt-4 space-y-2">
          <div>Kéo thả để sắp xếp thứ tự</div>
        </div>
      </SortableContext>
    </DndContext>
  );
}
