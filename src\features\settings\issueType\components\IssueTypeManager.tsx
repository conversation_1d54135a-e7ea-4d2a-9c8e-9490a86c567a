import { IssueTypeDialog } from "./IssueTypeDialog";
import { IssueTypeList } from "./IssueTypeList";

export function IssueTypeManager() {
  return (
    <>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Quản lý Issue Type
          </h1>
          <p className="text-gray-600">
            Tạo và quản lý các loại issue để phân loại vấn đề trong dự án.
          </p>
        </div>
      </div>
      <IssueTypeDialog mode="create" />
      <IssueTypeList />
    </>
  );
}
