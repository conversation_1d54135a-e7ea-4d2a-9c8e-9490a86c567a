"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { redirect } from "next/navigation"

export default function NewProject() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-6xl">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <FormSection  />
          <IllustrationSection />
        </div>
      </div>
    </div>
  )
}

function FormSection() {
  return (
    <div className="w-full max-w-md mx-auto lg:mx-0">
      <div className="space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Create Project</h1>
          <p className="text-muted-foreground text-lg">Plan your goal, milestone or team project</p>
        </div>

        <Separator />

        {/* Form Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">Project Details</CardTitle>
            <CardDescription>Enter the basic information for your new project</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Project Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium">
                Project Name *
              </Label>
              <Input id="name" placeholder="Try a team name, project goal, milestone..." className="h-11" />
              <p className="text-xs text-muted-foreground">Choose a descriptive name for your project</p>
            </div>

            {/* Project Key */}
            <div className="space-y-2">
              <Label htmlFor="project-key" className="text-sm font-medium">
                Project Key *
              </Label>
              <Input id="project-key" placeholder="ex: TEAM, GOAL123..." className="h-11 font-mono" />
              <p className="text-xs text-muted-foreground">
                A unique identifier for your project (uppercase recommended)
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col-reverse sm:flex-row gap-3 pt-4">
              <Button variant="outline" className="flex-1 bg-transparent">
                Cancel
              </Button>
              <Button type="submit" onClick={() => {
                redirect(`/project/TEAM`)
              }} disabled={false} className="flex-1">Create Project</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

function IllustrationSection() {
  return (
    <div className="w-full max-w-lg mx-auto lg:mx-0">
      <div className="relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 rounded-2xl transform rotate-1" />

        {/* Main illustration container */}
        <div className="relative bg-card border rounded-2xl p-8 shadow-sm">
          <img
            src="https://illustrations.popsy.co/emerald/man-riding-a-rocket.svg"
            alt="Project creation illustration"
            className="w-full h-auto object-contain"
          />
        </div>

        {/* Floating elements for visual interest */}
        <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary/10 rounded-full" />
        <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-secondary/10 rounded-full" />
      </div>

      {/* Additional context */}
      <div className="mt-8 text-center space-y-2">
        <h3 className="font-semibold text-lg">Get Started Quickly</h3>
        <p className="text-muted-foreground text-sm max-w-sm mx-auto">
          Create organized projects to track your goals, manage team tasks, and achieve milestones efficiently.
        </p>
      </div>
    </div>
  )
}
