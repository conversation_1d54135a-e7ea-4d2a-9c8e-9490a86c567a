"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { MessageSquare, GitCommit, UserPlus, FileText, GitPullRequest, Milestone, Bug, CheckCircle } from "lucide-react"

interface Activity {
  id: string
  type: number
  reason?: number
  actor: {
    name: string
    avatar?: string
  }
  content: {
    title: string
    description?: string
  }
  createdAt: string
  isUnread?: boolean
}

const mockActivities: Activity[] = [
  {
    id: "1",
    type: 3, // Issue Commented
    reason: 2, // Issue Commented
    actor: { name: "<PERSON>" },
    content: {
      title: "Commented on ISS-001",
      description: "Added clarification about the redirect behavior",
    },
    createdAt: "2 hours ago",
    isUnread: true,
  },
  {
    id: "2",
    type: 2, // Issue Updated
    actor: { name: "<PERSON>" },
    content: {
      title: "Updated ISS-002",
      description: "Changed priority from Low to Medium",
    },
    createdAt: "4 hours ago",
  },
  {
    id: "3",
    type: 12, // Git Pushed
    actor: { name: "<PERSON>" },
    content: {
      title: "Pushed to main branch",
      description: "3 commits: Fix login bug, Update tests, Add documentation",
    },
    createdAt: "6 hours ago",
  },
  {
    id: "4",
    type: 18, // Pull Request Added
    actor: { name: "Alice" },
    content: {
      title: "Created PR #45",
      description: "Feature: Add dark mode support",
    },
    createdAt: "1 day ago",
  },
  {
    id: "5",
    type: 22, // Milestone Created
    actor: { name: "Bob" },
    content: {
      title: "Created milestone v2.1.0",
      description: "Target date: March 15, 2024",
    },
    createdAt: "2 days ago",
  },
]

const getActivityIcon = (type: number) => {
  switch (type) {
    case 1:
      return <Bug className="h-4 w-4 text-red-500" /> // Issue Created
    case 2:
      return <FileText className="h-4 w-4 text-blue-500" /> // Issue Updated
    case 3:
      return <MessageSquare className="h-4 w-4 text-green-500" /> // Issue Commented
    case 12:
      return <GitCommit className="h-4 w-4 text-purple-500" /> // Git Pushed
    case 15:
      return <UserPlus className="h-4 w-4 text-indigo-500" /> // Project User Added
    case 18:
      return <GitPullRequest className="h-4 w-4 text-orange-500" /> // Pull Request Added
    case 22:
      return <Milestone className="h-4 w-4 text-yellow-500" /> // Milestone Created
    default:
      return <CheckCircle className="h-4 w-4 text-gray-500" />
  }
}

const getActivityText = (type: number) => {
  switch (type) {
    case 1:
      return "created issue"
    case 2:
      return "updated issue"
    case 3:
      return "commented on"
    case 12:
      return "pushed to"
    case 15:
      return "added user to"
    case 18:
      return "created pull request"
    case 22:
      return "created milestone"
    default:
      return "performed action on"
  }
}

export function ActivityFeed() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Recent Activity
        </CardTitle>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {mockActivities.map((activity) => (
            <div
              key={activity.id}
              className={`flex items-start gap-3 p-3 rounded-lg transition-colors ${
                activity.isUnread ? "bg-blue-50 border border-blue-200" : "hover:bg-muted/50"
              }`}
            >
              <Avatar className="h-8 w-8">
                <AvatarFallback className="text-xs">{activity.actor.name.charAt(0)}</AvatarFallback>
              </Avatar>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  {getActivityIcon(activity.type)}
                  <span className="text-sm font-medium">{activity.actor.name}</span>
                  <span className="text-sm text-muted-foreground">{getActivityText(activity.type)}</span>
                  {activity.isUnread && (
                    <Badge variant="secondary" className="text-xs">
                      New
                    </Badge>
                  )}
                </div>

                <p className="text-sm font-medium mb-1">{activity.content.title}</p>

                {activity.content.description && (
                  <p className="text-xs text-muted-foreground mb-2">{activity.content.description}</p>
                )}

                <span className="text-xs text-muted-foreground">{activity.createdAt}</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
