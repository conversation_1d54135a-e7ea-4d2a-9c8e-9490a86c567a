"use client";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState } from "react";
import { Status } from "@/shared/types/status";
import StatusForm from "./StatusForm";
interface StatusDialogProps {
  mode: "create" | "edit";
  status?: Status;
  trigger?: React.ReactNode;
}
export function StatusDialog({ mode, status, trigger }: StatusDialogProps) {
  const [open, setOpen] = useState(false);
  const handleSuccess = () => {
    setOpen(false);
  };
  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <>
      {trigger ? (
        <div onClick={() => setOpen(true)}>{trigger}</div>
      ) : (
        <Button onClick={() => setOpen(true)}>
          {mode === "create" ? "Tạo Status" : "Sửa Status"}
        </Button>
      )}
      <Dialog open={open} onOpenChange={setOpen}>
        
        <DialogContent>
          <DialogHeader>
            
            <DialogTitle>
              {mode === "create" ? "Tạo Status mới" : `Sửa "${status?.name}"`}
            </DialogTitle>
          </DialogHeader>
          <StatusForm
            status={status}
            mode={mode}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}

export default StatusDialog;
