"use client";

import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Settings,
  Users,
  Tags,
  Shield,
  Bell,
  Palette,
  Database,
  Key,
  ArrowRight,
  CheckCircle,
  AlertCircle,
} from "lucide-react";

const settingsCards = [
  {
    title: "Categories",
    description: "Quản lý phân loại issues và tổ chức dự án",
    icon: Tags,
    href: "/settings/categories",
    status: "active",
    count: "12 categories",
    color: "bg-blue-500",
  },
  {
    title: "Người dùng",
    description: "Quản lý tài khoản và thông tin người dùng",
    icon: Users,
    href: "/settings/users",
    status: "active",
    count: "24 users",
    color: "bg-green-500",
  },
  {
    title: "Quyền hạn",
    description: "<PERSON><PERSON><PERSON> h<PERSON><PERSON> quyền t<PERSON>y cập và vai trò",
    icon: Shield,
    href: "/settings/permissions",
    status: "needs-attention",
    count: "5 roles",
    color: "bg-orange-500",
  },
  {
    title: "Thông báo",
    description: "Cài đặt thông báo email và push notification",
    icon: Bell,
    href: "/settings/notifications",
    status: "active",
    count: "3 templates",
    color: "bg-purple-500",
  },
  {
    title: "Giao diện",
    description: "Tùy chỉnh theme và layout của ứng dụng",
    icon: Palette,
    href: "/settings/appearance",
    status: "active",
    count: "2 themes",
    color: "bg-pink-500",
  },
  {
    title: "Cơ sở dữ liệu",
    description: "Quản lý backup và migration dữ liệu",
    icon: Database,
    href: "/settings/database",
    status: "active",
    count: "Last backup: 2h ago",
    color: "bg-indigo-500",
  },
  {
    title: "API Keys",
    description: "Quản lý khóa API và webhook endpoints",
    icon: Key,
    href: "/settings/api-keys",
    status: "active",
    count: "3 active keys",
    color: "bg-gray-500",
  },
];

const systemStats = [
  {
    label: "Tổng số dự án",
    value: "12",
    change: "+2 tuần này",
    trend: "up",
  },
  {
    label: "Issues đang mở",
    value: "48",
    change: "-5 từ tuần trước",
    trend: "down",
  },
  {
    label: "Người dùng hoạt động",
    value: "18",
    change: "+3 tuần này",
    trend: "up",
  },
  {
    label: "Storage sử dụng",
    value: "2.4 GB",
    change: "của 10 GB",
    trend: "neutral",
  },
];

export default function SettingsPage() {
  return (
    <div className="container mx-auto py-6 px-4 space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Cài đặt hệ thống</h1>
        <p className="text-gray-600 mt-2">
          Quản lý và cấu hình các thành phần của hệ thống quản lý dự án.
        </p>
      </div>

      {/* System Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {systemStats.map((stat) => (
          <Card key={stat.label}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className="text-right">
                  <p className={`text-xs ${
                    stat.trend === 'up' ? 'text-green-600' : 
                    stat.trend === 'down' ? 'text-red-600' : 
                    'text-gray-500'
                  }`}>
                    {stat.change}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Settings Cards */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Cài đặt chi tiết</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {settingsCards.map((card) => (
            <Card key={card.title} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${card.color} text-white`}>
                      <card.icon className="h-5 w-5" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{card.title}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        {card.status === "active" ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-orange-500" />
                        )}
                        <Badge 
                          variant={card.status === "active" ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {card.status === "active" ? "Hoạt động" : "Cần chú ý"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="mb-4">
                  {card.description}
                </CardDescription>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">{card.count}</span>
                  <Link href={card.href}>
                    <Button variant="ghost" size="sm" className="gap-2">
                      Cấu hình
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Hành động nhanh
          </CardTitle>
          <CardDescription>
            Các tác vụ thường dùng để quản lý hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link href="/settings/categories">
              <Button variant="outline" className="w-full justify-start gap-2">
                <Tags className="h-4 w-4" />
                Tạo Category mới
              </Button>
            </Link>
            <Link href="/settings/users">
              <Button variant="outline" className="w-full justify-start gap-2">
                <Users className="h-4 w-4" />
                Mời người dùng
              </Button>
            </Link>
            <Link href="/settings/database">
              <Button variant="outline" className="w-full justify-start gap-2">
                <Database className="h-4 w-4" />
                Backup dữ liệu
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
