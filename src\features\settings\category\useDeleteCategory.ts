import { useMutation, useQueryClient } from "@tanstack/react-query";
import { categoryService } from "@/services/categoryService";
import { categoryQueryKeys, useCachedCategory } from "./useCategoryQuery";
import { toast } from "sonner";

export const useDeleteCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => categoryService.deleteCategory(id),
    onSuccess: (_, deletedId) => {
      // Get category name from cache for toast message
      const cachedCategory = queryClient.getQueryData(
        categoryQueryKeys.detail(deletedId)
      )?.data;

      // Remove from cache
      queryClient.removeQueries({ queryKey: categoryQueryKeys.detail(deletedId) });

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.stats() });

      const categoryName = cachedCategory?.name || "Category";
      toast.success(`${categoryName} đã được xóa thành công!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi xóa category: ${error.message}`);
    },
  });
};

// Hook để bulk delete categories
export const useBulkDeleteCategories = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => categoryService.bulkDeleteCategories(ids),
    onSuccess: (_, deletedIds) => {
      // Remove from cache
      deletedIds.forEach(id => {
        queryClient.removeQueries({ queryKey: categoryQueryKeys.detail(id) });
      });

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.stats() });

      toast.success(`Đã xóa ${deletedIds.length} category thành công!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi xóa categories: ${error.message}`);
    },
  });
};

// Hook để duplicate category
export const useDuplicateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => categoryService.duplicateCategory(id),
    onSuccess: (response, originalId) => {
      // Get original category name from cache
      const originalCategory = queryClient.getQueryData(
        categoryQueryKeys.detail(originalId)
      )?.data;

      // Set new category in cache
      queryClient.setQueryData(
        categoryQueryKeys.detail(response.data.id),
        response
      );

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.stats() });

      const originalName = originalCategory?.name || "Category";
      toast.success(`Đã sao chép "${originalName}" thành công!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi sao chép category: ${error.message}`);
    },
  });
};