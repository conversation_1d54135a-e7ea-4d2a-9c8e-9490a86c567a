import { issueTypeService, CreateIssueTypeRequest } from "@/services/issueTypeService";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export const useIssueType = () => {
  return useQuery({
    queryKey: ["issueTypes"],
    queryFn: issueTypeService.getAll,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

export const useCreateIssueType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: CreateIssueTypeRequest) => issueTypeService.createIssueType(data),
    onSuccess: (issueType) => {
      // Force refetch để đảm bảo data update
      queryClient.refetchQueries({ queryKey: ["issueTypes"] });
      toast.success(`Issue Type "${issueType.name}" đã được tạo thành công!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi tạo Issue Type: ${error.message}`);
    },
  });
};
