import { IssueType } from "@/shared/types/IssueType";

const mockIssueTypes: IssueType[] = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    color: "#EF4444",
    description: "Lỗi hệ thống, c<PERSON>n x<PERSON> lý gấp",
    order: 1,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1
  },
  {
    id: 2,
    name: "Feature",
    color: "#3B82F6",
    description: "<PERSON><PERSON><PERSON> cầu tính năng mới",
    order: 2,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1
  },
  {
    id: 3,
    name: "Enhancement",
    color: "#10B981",
    description: "<PERSON><PERSON><PERSON> tiến tính năng hiện có",
    order: 3,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1
  },
  {
    id: 4,
    name: "Task",
    color: "#6366F1",
    description: "Công việc thường xuyên",
    order: 4,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1
  },
  {
    id: 5,
    name: "Documentation",
    color: "#F59E0B",
    description: "Cập nhật tài liệu dự án",
    order: 5,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1
  },
  {
    id: 6,
    name: "Research",
    color: "#14B8A6",
    description: "Khảo sát giải pháp hoặc xu hướng",
    order: 6,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1
  }
];
// Type cho create request (không có id)
export type CreateIssueTypeRequest = {
  name: string;
  description?: string;
  color: string;
  active: boolean;
};

export const issueTypeService = {
  getAll: async (): Promise<IssueType[]> => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));
    return [...mockIssueTypes]; // Return copy
  },

  // Create issueType
  // createIssueType: async (data: CreateIssueTypeRequest): Promise<IssueType> => {
  //   // Simulate API delay
  //   await new Promise((resolve) => setTimeout(resolve, 800));

  //   // Tạo IssueType mới với ID
  //   const newIssueType: IssueType = {
  //     id: crypto.randomUUID(),
  //     name: data.name,
  //     description: data.description,
  //     color: data.color,
  //     active: data.active,
  //   };

  //   // Add to mock database
  //   mockIssueTypes.push(newIssueType);

  //   console.log("✅ Created new issue type:", newIssueType);
  //   console.log("📋 Current mock data:", mockIssueTypes);

  //   return newIssueType;
  // },

  // Reorder issueTypes
  reorderIssueTypes: async (input: IssueType[]): Promise<void> => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 800));
    // Update mock database
    mockIssueTypes.sort((a, b) => {
      const aIndex = input.findIndex((i) => i.id === a.id);
      const bIndex = input.findIndex((i) => i.id === b.id);
      return aIndex - bIndex;
    });
  },
};
