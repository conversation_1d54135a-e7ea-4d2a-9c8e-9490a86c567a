import { IssueType, UpdateIssueTypeRequest } from "@/shared/types/IssueType";

const mockIssueTypes: IssueType[] = [
  {
    id: 1,
    name: "Bug",
    color: "#EF4444",
    description: "Lỗi hệ thống, cần x<PERSON> lý gấp",
    order: 1,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
  {
    id: 2,
    name: "Feature",
    color: "#3B82F6",
    description: "<PERSON><PERSON><PERSON> c<PERSON>u t<PERSON>h năng mới",
    order: 2,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
  {
    id: 3,
    name: "Enhancement",
    color: "#10B981",
    description: "<PERSON><PERSON>i t<PERSON>ế<PERSON> t<PERSON>h năng hiện có",
    order: 3,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
  {
    id: 4,
    name: "Task",
    color: "#6366F1",
    description: "Công việc thường xuyên",
    order: 4,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
  {
    id: 5,
    name: "Documentation",
    color: "#F59E0B",
    description: "Cập nhật tài liệu dự án",
    order: 5,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
  {
    id: 6,
    name: "Research",
    color: "#14B8A6",
    description: "Khảo sát giải pháp hoặc xu hướng",
    order: 6,
    projectId: 1,
    projectKey: "PRM",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    createdById: 1,
    lastModifiedAt: new Date("2024-01-01"),
    lastModifiedById: 1,
  },
];
// Type cho create request (không có id)
export type CreateIssueTypeRequest = {
  name: string;
  description?: string;
  color: string;
  active: boolean;
};

export const issueTypeService = {
  getAll: async (): Promise<IssueType[]> => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));
    return [...mockIssueTypes]; // Return copy
  },

  createIssueType: async (data: CreateIssueTypeRequest): Promise<IssueType> => {
    await new Promise((resolve) => setTimeout(resolve, 500));

    const existingIssueType = mockIssueTypes.find(
      (issueType) => issueType.name.toLowerCase() === data.name.toLowerCase()
    );
    if (existingIssueType) {
      throw new Error(`IssueType với tên "${data.name}" đã tồn tại`);
    }

    const newIssueType: IssueType = {
      id: Math.max(...mockIssueTypes.map((i) => i.id)) + 1,
      name: data.name,
      description: data.description,
      color: data.color,
      isActive: data.active,
      order: mockIssueTypes.length + 1,
      projectId: 1,
      projectKey: "PRM",
      createdAt: new Date(),
      createdById: 1,
      lastModifiedAt: new Date(),
      lastModifiedById: 1,
    };

    mockIssueTypes.push(newIssueType);
    return newIssueType;
  },

  updateIssueType: async (data: UpdateIssueTypeRequest): Promise<IssueType> => {
    await new Promise((resolve) => setTimeout(resolve, 500));

    const existingIssueType = mockIssueTypes.find((issueType) => issueType.id === data.id);
    if (!existingIssueType) {
      throw new Error(`IssueType với ID ${data.id} không tồn tại`);
    }
    const updatedIssueType: IssueType = {
      ...existingIssueType,
      name: data.name,
      description: data.description,
      color: data.color,
      isActive: data.active,
      lastModifiedAt: new Date(),
      lastModifiedById: 1,
    };

    mockIssueTypes[mockIssueTypes.indexOf(existingIssueType)] = updatedIssueType;
    return updatedIssueType;
  },

  deleteIssueType: async (id: number ): Promise<void> => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    const index = mockIssueTypes.findIndex((issueType) => issueType.id === id);
    if (index === -1) {
      throw new Error(`IssueType với ID ${id} không tồn tại`);
    }

    mockIssueTypes.splice(index, 1);
  },

  // Reorder issueTypes
  reorderIssueTypes: async (input: IssueType[]): Promise<void> => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 800));
    // Update mock database
    mockIssueTypes.sort((a, b) => {
      const aIndex = input.findIndex((i) => i.id === a.id);
      const bIndex = input.findIndex((i) => i.id === b.id);
      return aIndex - bIndex;
    });
  },
};
