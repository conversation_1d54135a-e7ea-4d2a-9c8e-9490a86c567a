import { IssueType } from "@/shared/types/issueTypes";

const mockIssueTypes: IssueType[] = [
  {
    id: "1",
    name: "<PERSON><PERSON>",
    color: "#EF4444",
    description: "Lỗi hệ thống, cần x<PERSON> lý gấp",
    active: true,
  },
  {
    id: "2",
    name: "Feature",
    color: "#3B82F6",
    description: "<PERSON><PERSON><PERSON> cầu tính năng mới",
    active: true,
  },
  {
    id: "3",
    name: "Enhancement",
    color: "#10B981",
    description: "Cải tiến tính năng hiện có",
    active: true,
  },
  {
    id: "4",
    name: "Task",
    color: "#6366F1",
    description: "<PERSON><PERSON>ng việc thường xuyên",
    active: true,
  },
  {
    id: "5",
    name: "Documentation",
    color: "#F59E0B",
    description: "Cập nhật tài liệu dự án",
    active: false,
  },
  {
    id: "6",
    name: "Research",
    color: "#14B8A6",
    description: "Khảo sát giải pháp hoặc xu hướng",
    active: true,
  },
  {
    id: "7",
    name: "UI Fix",
    color: "#E879F9",
    description: "Sửa lỗi giao diện người dùng",
    active: true,
  },
  {
    id: "8",
    name: "Backend Issue",
    color: "#F87171",
    description: "Lỗi phía máy chủ hoặc API",
    active: true,
  },
];
// Type cho create request (không có id)
export type CreateIssueTypeRequest = {
  name: string;
  description?: string;
  color: string;
  active: boolean;
};

export const issueTypeService = {
  getAll: async (): Promise<IssueType[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...mockIssueTypes]; // Return copy
  },

  // Create issueType
  createIssueType: async (data: CreateIssueTypeRequest): Promise<IssueType> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Tạo IssueType mới với ID
    const newIssueType: IssueType = {
      id: crypto.randomUUID(),
      name: data.name,
      description: data.description,
      color: data.color,
      active: data.active,
    };

    // Add to mock database
    mockIssueTypes.push(newIssueType);

    console.log("✅ Created new issue type:", newIssueType);
    console.log("📋 Current mock data:", mockIssueTypes);

    return newIssueType;
  },
};
