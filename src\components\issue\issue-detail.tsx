"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { <PERSON>, Link2, Loader2, <PERSON><PERSON><PERSON>, ArrowUpRight } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { toast } from "sonner";

// Mock data type
interface Issue {
  id: string;
  title: string;
  description: string;
  projectIssueKey: string;
  projectKey: string;
  dueDate?: string;
  issueType?: {
    id: string;
    name: string;
    color: string;
  };
  issueStatus?: {
    id: string;
    name: string;
    color: string;
  };
  issuePriority?: {
    id: string;
    name: string;
    color: string;
  };
  reporter?: {
    id: string;
    name: string;
    avatar?: string;
  };
  assignee?: {
    id: string;
    name: string;
    avatar?: string;
  };
  issueCategories?: Array<{
    category?: {
      id: string;
      name: string;
    };
  }>;
  issueVersions?: Array<{
    version?: {
      id: string;
      name: string;
    };
  }>;
  issueFile?: Array<{
    id: string;
    name: string;
    url: string;
  }>;
}

// Mock data
const mockIssue: Issue = {
  id: "1",
  title: "Fix bug in login system",
  description:
    "<p>User is unable to login after the last update. The SSL certificate appears to be expired.</p>",
  projectIssueKey: "PROJ-123",
  projectKey: "PROJ",
  dueDate: "2025-07-25",
  issueType: {
    id: "1",
    name: "Bug",
    color: "bg-red-100 text-red-800",
  },
  issueStatus: {
    id: "1",
    name: "In Progress",
    color: "bg-blue-500",
  },
  issuePriority: {
    id: "1",
    name: "High",
    color: "bg-red-100 text-red-800",
  },
  reporter: {
    id: "1",
    name: "John Doe",
    avatar: "/placeholder.svg?height=32&width=32",
  },
  assignee: {
    id: "2",
    name: "Jane Smith",
    avatar: "/placeholder.svg?height=32&width=32",
  },
  issueCategories: [
    {
      category: {
        id: "1",
        name: "Backend",
      },
    },
    {
      category: {
        id: "2",
        name: "Security",
      },
    },
  ],
  issueVersions: [
    {
      version: {
        id: "1",
        name: "v1.2.3",
      },
    },
  ],
  issueFile: [
    {
      id: "1",
      name: "error_screenshot.png",
      url: "/placeholder.svg",
    },
  ],
};

export default function IssueDetail({
  data = mockIssue,
  isReviewMode = false,
}: {
  data?: Issue;
  isReviewMode?: boolean;
}) {
  const [watching, setWatching] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isWatchingLoading, setIsWatchingLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);

  // Mock user context
  const userId = "user123";

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsPageLoading(false);
      setIsWatchingLoading(true);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isPageLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  const copyToClipboard = (text: string) => {
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard
        .writeText(text)
        .then(() => toast.success("Link copied to clipboard"))
        .catch((err) => {
          console.error(err);
          toast.error("Failed to copy link");
        });
    } else {
      // Fallback for HTTP or unsupported browsers
      const textArea = document.createElement("textarea");
      textArea.value = text;
      textArea.style.position = "fixed";
      textArea.style.opacity = "0";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        const success = document.execCommand("copy");
        if (success) {
          toast.success("Link copied to clipboard");
        } else {
          toast.error("Browser doesn't support clipboard copy");
        }
      } catch (err) {
        console.error("Fallback copy failed", err);
        toast.error("Error occurred while copying");
      }
      document.body.removeChild(textArea);
    }
  };

  return (
    <div className=" ">
      <div className="container mx-auto mw-[95%] ">
        {/* Header */}
        <div className="bg-white rounded-lg border p-6 mb-8">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Badge
                  className={`${data?.issueType?.color} text-sm font-medium`}
                >
                  {data?.issueType?.name}
                </Badge>
                <h3 className="font-bold text-lg text-gray-900">
                  {data?.projectIssueKey}
                </h3>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex gap-2">
                {!isReviewMode && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() =>
                      copyToClipboard(
                        `${window.location.origin}/projects/${data?.projectKey}/issue/${data?.projectIssueKey}`
                      )
                    }
                  >
                    <Link2 className="w-4 h-4 mr-2" />
                    Copy
                  </Button>
                )}
                {!isReviewMode && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={async () => {
                      setIsLoading(true);
                      // Simulate API call
                      setTimeout(() => {
                        setWatching(!watching);
                        setIsLoading(false);
                        toast.success(
                          watching ? "Unwatched issue" : "Watching issue"
                        );
                      }, 1000);
                    }}
                  >
                    {isLoading ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Eye className="w-4 h-4 mr-2" />
                    )}
                    {watching ? "Unwatch" : "Watch"}
                  </Button>
                )}
                {!isReviewMode && (
                  <Link
                    href={`/projects/${data?.projectKey}/issue/${data?.projectIssueKey}/edit`}
                  >
                    <Button
                      size="sm"
                      className="bg-gray-900 hover:bg-gray-800 text-white"
                    >
                      <Pencil className="w-4 h-4 mr-2" />
                      Edit
                    </Button>
                  </Link>
                )}
              </div>
              <div className="flex items-center gap-3 text-sm">
                <Badge variant="outline" className="bg-gray-100">
                  Due Date:{" "}
                  {data?.dueDate
                    ? new Date(data.dueDate).toLocaleDateString()
                    : "Not set"}
                </Badge>
                <Badge className={`${data?.issueStatus?.color} text-white`}>
                  {data?.issueStatus?.name}
                </Badge>
              </div>
            </div>
          </div>
          <div className="mt-6">
            <h1 className="text-2xl font-bold text-gray-900">{data?.title}</h1>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card className="border">
              <CardHeader className="border-b bg-gray-50 flex flex-row items-center gap-3">
                <Link
                  href={`/users/${data?.reporter?.id}`}
                  className="flex items-center gap-3 hover:opacity-80"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={data?.reporter?.avatar || "/placeholder.svg"}
                    />
                    <AvatarFallback className="bg-gray-200 text-gray-700">
                      {data?.reporter?.name?.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-medium text-gray-700">
                    {data?.reporter?.name}
                  </span>
                </Link>
              </CardHeader>
              <CardContent className="p-6">
                <div className="prose max-w-none">
                  <div
                    dangerouslySetInnerHTML={{
                      __html: data?.description || "",
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card className="border">
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div className="text-sm font-medium text-gray-700">
                      Priority
                    </div>
                    <div className="col-span-2 flex items-center gap-2">
                      <ArrowUpRight className="text-gray-500 h-4 w-4" />
                      <Badge
                        variant="outline"
                        className={data?.issuePriority?.color}
                      >
                        {data?.issuePriority?.name}
                      </Badge>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div className="text-sm font-medium text-gray-700">
                      Category
                    </div>
                    <div className="col-span-2">
                      <div className="flex flex-wrap gap-2">
                        {data?.issueCategories &&
                        data.issueCategories.length > 0 ? (
                          data.issueCategories.map((items) => (
                            <Badge
                              key={items.category?.id}
                              className="bg-gray-100 text-gray-700"
                            >
                              {items.category?.name}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-sm text-gray-500">
                            No categories assigned
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div className="text-sm font-medium text-gray-700">
                      Version
                    </div>
                    <div className="col-span-2">
                      {data?.issueVersions && data.issueVersions.length > 0 ? (
                        data.issueVersions.map((item) => (
                          <Badge
                            key={item.version?.id}
                            variant="outline"
                            className="bg-gray-50 hover:bg-gray-100 mr-2"
                          >
                            {item.version?.name}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-sm text-gray-500">
                          No versions assigned
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border">
              <CardContent className="p-6">
                <div className="grid grid-cols-3 gap-4 items-center">
                  <div className="text-sm font-medium text-gray-700">
                    Assignee
                  </div>
                  <div className="col-span-2 flex items-center gap-2">
                    {data?.assignee ? (
                      <>
                        <Avatar className="h-8 w-8">
                          <AvatarImage
                            src={data.assignee.avatar || "/placeholder.svg"}
                          />
                          <AvatarFallback className="bg-gray-200 text-gray-700">
                            {data.assignee.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm font-medium text-gray-700">
                          {data.assignee.name}
                        </span>
                      </>
                    ) : (
                      <span className="text-sm text-gray-500">No assignee</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* File Attachments */}
        {isReviewMode && data?.issueFile && data.issueFile.length > 0 && (
          <Card className="mt-8 border">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">
                Attachments
              </h3>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {data.issueFile.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                        <svg
                          className="w-5 h-5 text-gray-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">File</p>
                    </div>
                    <Button size="sm" variant="ghost">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
