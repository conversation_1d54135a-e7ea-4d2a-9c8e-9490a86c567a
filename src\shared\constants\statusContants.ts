// Category validation constants
export const STATUS_VALIDATION = {
  NAME_MIN_LENGTH: 1,
  NAME_MAX_LENGTH: 50,
  DESCRIPTION_MAX_LENGTH: 500,
  ORDER_MIN: 1,
  ORDER_MAX: 9999,
  MAX_BULK_OPERATIONS: 50,
  MAX_REORDER_ITEMS: 100,
} as const;

// Error messages
export const STATUS_ERROR_MESSAGES = {
  NAME_REQUIRED: "Tên status không được để trống",
  NAME_TOO_LONG: `Tên status không được quá ${STATUS_VALIDATION.NAME_MAX_LENGTH} ký tự`,
  NAME_INVALID_CHARS:
    "Tên status chỉ được chứa chữ cái, số, khoảng trắng và các ký tự: - _ .",
  NAME_ONLY_WHITESPACE: "Tên status không được chỉ chứa khoảng trắng",
  DESCRIPTION_TOO_LONG: `Mô tả không được quá ${STATUS_VALIDATION.DESCRIPTION_MAX_LENGTH} ký tự`,
  COLOR_INVALID_FORMAT: "Màu sắc phải là mã hex hợp lệ (ví dụ: #FF0000)",
  COLOR_INVALID_BRIGHTNESS: "Màu sắc không phù hợp cho hiển thị text",
  ID_INVALID: "ID status không hợp lệ",
  ID_INVALID_UUID: "ID status phải là UUID hợp lệ",
  ORDER_INVALID: `Thứ tự phải từ ${STATUS_VALIDATION.ORDER_MIN} đến ${STATUS_VALIDATION.ORDER_MAX}`,
  BULK_TOO_MANY: `Không thể thao tác quá ${STATUS_VALIDATION.MAX_BULK_OPERATIONS} status cùng lúc`,
  REORDER_TOO_MANY: `Không thể sắp xếp quá ${STATUS_VALIDATION.MAX_REORDER_ITEMS} status cùng lúc`,
  STATUS_IN_USE: "Không thể xóa status đang được sử dụng",
  STATUS_NOT_FOUND: "Status không tồn tại",
  STATUS_NAME_EXISTS: "Tên status đã tồn tại",
} as const;

// Default category colors
export const DEFAULT_CATEGORY_COLORS = [
  "#3B82F6", // Blue
  "#10B981", // Green
  "#EF4444", // Red
  "#F59E0B", // Yellow
  "#8B5CF6", // Purple
  "#06B6D4", // Cyan
  "#EC4899", // Pink
  "#84CC16", // Lime
  "#F97316", // Orange
  "#6B7280", // Gray
  "#1F2937", // Dark Gray
  "#7C3AED", // Violet
  "#DC2626", // Red Dark
  "#059669", // Green Dark
  "#0EA5E9", // Sky Blue
  "#6366F1", // Indigo
  "#10B981", // Green Light
  "#F59E0B", // Yellow Light
  "#EF4444", // Red Light
  "#8B5CF6", // Purple Light
  "#06B6D4", // Cyan Light
  "#EC4899", // Pink Light
] as const;

// Category status options
export const CATEGORY_STATUS = {
  ACTIVE: true,
  INACTIVE: false,
} as const;

// Sort field options
export const CATEGORY_SORT_FIELDS = [
  "name",
  "createdAt",
  "updatedAt",
  "order",
  "issueCount",
] as const;

// Sort direction options
export const SORT_DIRECTIONS = ["asc", "desc"] as const;

// Pagination defaults
export const PAGINATION_DEFAULTS = {
  PAGE: 1,
  LIMIT: 10,
  MAX_LIMIT: 100,
} as const;

// Color brightness validation
export const COLOR_VALIDATION = {
  MIN_BRIGHTNESS: 30,
  MAX_BRIGHTNESS: 225,
} as const;

// Category form field names
export const CATEGORY_FORM_FIELDS = {
  NAME: "name",
  DESCRIPTION: "description",
  COLOR: "color",
  IS_ACTIVE: "isActive",
  PROJECT_ID: "projectId",
  TAGS: "tags",
  ORDER: "order",
} as const;
