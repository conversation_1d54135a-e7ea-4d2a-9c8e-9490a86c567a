"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Settings,
  Users,
  Tags,
  Shield,
  Bell,
  Palette,
  Database,
  Key,
  Menu,
  X,
} from "lucide-react";
import { cn } from "@/lib/utils";

const settingsNavigation = [
  {
    name: "Tổng quan",
    href: "/settings",
    icon: Settings,
    description: "Cài đặt chung của hệ thống",
  },
  {
    name: "Categories",
    href: "/settings/categories",
    icon: Tags,
    description: "Quản lý phân loại issues",
  },
  {
    name: "Người dùng",
    href: "/settings/users",
    icon: Users,
    description: "Quản lý tài khoản người dùng",
  },
  {
    name: "<PERSON>uyền hạn",
    href: "/settings/permissions",
    icon: Shield,
    description: "<PERSON><PERSON><PERSON> hình quyền truy cập",
  },
  {
    name: "<PERSON>hô<PERSON> báo",
    href: "/settings/notifications",
    icon: Bell,
    description: "Cài đặt thông báo",
  },
  {
    name: "Giao diện",
    href: "/settings/appearance",
    icon: Palette,
    description: "Tùy chỉnh giao diện",
  },
  {
    name: "Cơ sở dữ liệu",
    href: "/settings/database",
    icon: Database,
    description: "Quản lý dữ liệu",
  },
  {
    name: "API Keys",
    href: "/settings/api-keys",
    icon: Key,
    description: "Quản lý khóa API",
  },
];

interface SettingsLayoutProps {
  children: React.ReactNode;
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <div className="flex">
        {/* Sidebar */}
        <div
          className={cn(
            "fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
            sidebarOpen ? "translate-x-0" : "-translate-x-full"
          )}
        >
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Cài đặt</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
              {settingsNavigation.map((item) => {
                const isActive = pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => setSidebarOpen(false)}
                    className={cn(
                      "flex items-start gap-3 px-3 py-2 rounded-lg text-sm transition-colors",
                      isActive
                        ? "bg-blue-50 text-blue-700 border border-blue-200"
                        : "text-gray-700 hover:bg-gray-100"
                    )}
                  >
                    <item.icon
                      className={cn(
                        "h-5 w-5 mt-0.5 flex-shrink-0",
                        isActive ? "text-blue-600" : "text-gray-500"
                      )}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium">{item.name}</div>
                      <div className="text-xs text-gray-500 mt-0.5">
                        {item.description}
                      </div>
                    </div>
                  </Link>
                );
              })}
            </nav>

            {/* Footer */}
            <div className="p-4 border-t border-gray-200">
              <div className="text-xs text-gray-500">
                <p>Version 1.0.0</p>
                <p className="mt-1">© 2024 Project Management</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 lg:ml-0">
          {/* Mobile header */}
          <div className="lg:hidden bg-white border-b border-gray-200 px-4 py-3">
            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </Button>
              <h1 className="text-lg font-semibold text-gray-900">Cài đặt</h1>
              <div className="w-8" /> {/* Spacer */}
            </div>
          </div>

          {/* Page content */}
          <main className="flex-1">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
}
