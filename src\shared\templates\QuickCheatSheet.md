# 🚀 CHEAT SHEET - LÀM FEATURE MỚI

## 📋 CHECKLIST 5 BƯỚC

```
□ 1. Tạo Types (data structure)
□ 2. Tạo Service (mock API)  
□ 3. <PERSON><PERSON><PERSON> Hooks (business logic)
□ 4. Tạo Components (UI)
□ 5. T<PERSON>o Page (kết n<PERSON><PERSON> tất cả)
```

## 🎯 THỨ TỰ FOLDER

```
src/
├── shared/types/           # Bước 1: Types
├── services/              # Bước 2: API calls
├── features/[feature]/    # Bước 3-4: Hooks + Components
└── app/[feature]/         # Bước 5: Pages
```

## ⚡ TEMPLATE NHANH

### 1. Types (5 phút)
```typescript
export type MyThing = {
  id: string;
  name: string;
  // thêm fields khác
};

export type CreateMyThingRequest = {
  name: string;
  // chỉ required fields
};
```

### 2. Service (10 phút)
```typescript
const mockData = [{ id: "1", name: "Test" }];

export const myService = {
  getAll: async () => {
    await new Promise(r => setTimeout(r, 1000));
    return mockData;
  },
  
  create: async (data) => {
    await new Promise(r => setTimeout(r, 800));
    const newItem = { id: crypto.randomUUID(), ...data };
    mockData.push(newItem);
    return newItem;
  }
};
```

### 3. Hooks (10 phút)
```typescript
export const useMyThings = () => {
  return useQuery({
    queryKey: ["myThings"],
    queryFn: myService.getAll
  });
};

export const useCreateMyThing = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: myService.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["myThings"] });
      toast.success("Thành công!");
    }
  });
};
```

### 4. Component (15 phút)
```typescript
export function MyThingList() {
  const { data, isLoading } = useMyThings();
  
  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      {data?.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
}
```

### 5. Page (5 phút)
```typescript
export default function MyThingPage() {
  return (
    <div className="container mx-auto py-6">
      <h1>My Things</h1>
      <MyThingList />
    </div>
  );
}
```

## 🔥 TIPS QUAN TRỌNG

### ✅ LÀM
- Bắt đầu với mock data
- Test từng bước nhỏ
- Copy template và sửa tên
- Hỏi khi không hiểu

### ❌ TRÁNH
- Viết quá nhiều code cùng lúc
- Làm UI đẹp trước khi logic chạy
- Copy code không hiểu
- Bỏ qua error handling

## 🎨 UI COMPONENTS CƠ BẢN

```typescript
// Loading
if (isLoading) return <div>Đang tải...</div>;

// Error  
if (error) return <div>Lỗi: {error.message}</div>;

// Empty
if (!data?.length) return <div>Chưa có dữ liệu</div>;

// List
return (
  <div className="space-y-4">
    {data.map(item => (
      <Card key={item.id}>
        <CardContent>{item.name}</CardContent>
      </Card>
    ))}
  </div>
);
```

## 🚨 KHI GẶP LỖI

1. **Import lỗi** → Kiểm tra đường dẫn file
2. **Type lỗi** → Kiểm tra interface/type
3. **Hook lỗi** → Đảm bảo wrap trong QueryClient
4. **UI lỗi** → Kiểm tra props và data structure

## 📚 PATTERN THƯỜNG DÙNG

### CRUD Operations
```typescript
// C - Create
const useCreate = () => useMutation({ mutationFn: service.create });

// R - Read  
const useList = () => useQuery({ queryKey: ["items"], queryFn: service.getAll });

// U - Update
const useUpdate = () => useMutation({ mutationFn: service.update });

// D - Delete
const useDelete = () => useMutation({ mutationFn: service.delete });
```

### Form Handling
```typescript
const [value, setValue] = useState("");

const handleSubmit = (e) => {
  e.preventDefault();
  if (!value.trim()) {
    toast.error("Không được để trống");
    return;
  }
  createMutation.mutate({ name: value });
};
```

### Error + Loading States
```typescript
const { data, isLoading, error } = useQuery(...);

if (isLoading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
if (!data?.length) return <EmptyState />;

return <DataList data={data} />;
```

---

## 💡 REMEMBER

**"Code phức tạp = Nhiều pattern đơn giản ghép lại"**

Bắt đầu với 1 pattern đơn giản → Chạy được → Copy và sửa cho case khác → Dần dần sẽ quen!

**Đừng sợ code xấu, refactor sau. Quan trọng là làm được trước!** 💪
