"use client";

import { useState, useMemo, useEffect, useRef, useCallback } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { CommentsSection, type Comment } from "@/components/home/<USER>";
import {
  Bug,
  FlagIcon as Feature,
  CheckSquare,
  AlertCircle,
  Calendar,
  Search,
  Filter,
  ArrowUpDown,
  History,
  ArrowRight,
  Heart,
} from "lucide-react";

interface IssueChange {
  field: string;
  oldValue: string;
  newValue: string;
  timestamp: string;
}

interface Issue {
  id: string;
  title: string;
  description: string;
  status: "Open" | "In Progress" | "In Review" | "Done" | "Blocked";
  type: "Bug" | "Feature" | "Task" | "Improvement";
  priority: "Critical" | "High" | "Medium" | "Low";
  assignee: string;
  reporter: string;
  createdAt: string;
  updatedAt: string;
  assigneeAvatar?: string;
  reporterAvatar?: string;
  changes?: IssueChange[];
  comments?: Comment[];
  likes: number;
  likedBy: string[];
}

// Extended mock data with issue likes
const mockIssues: Issue[] = [
  {
    id: "ISS-001",
    title: "Fix login redirect bug",
    description: "Users are redirected to wrong page after Google login",
    status: "In Progress",
    type: "Bug",
    priority: "High",
    assignee: "Alice",
    reporter: "Bob",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    reporterAvatar: "/placeholder.svg?height=32&width=32",
    likes: 8,
    likedBy: [
      "Alice",
      "Charlie",
      "David",
      "Eve",
      "Frank",
      "Grace",
      "Henry",
      "Ivy",
    ],
    changes: [
      {
        field: "status",
        oldValue: "Open",
        newValue: "In Progress",
        timestamp: "2024-01-20",
      },
      {
        field: "assignee",
        oldValue: "Unassigned",
        newValue: "Alice",
        timestamp: "2024-01-18",
      },
    ],
    comments: [
      {
        id: "c1",
        author: "Bob",
        authorAvatar: "/placeholder.svg?height=32&width=32",
        content:
          "I've reproduced this issue. It happens specifically with Google OAuth when users have multiple accounts.",
        createdAt: "2024-01-16",
      },
      {
        id: "c2",
        author: "Alice",
        authorAvatar: "/placeholder.svg?height=32&width=32",
        content:
          "Thanks for the details! I'm looking into the OAuth callback handling. Should have a fix ready by tomorrow.",
        createdAt: "2024-01-18",
        replies: [
          {
            id: "c2-r1",
            author: "Charlie",
            authorAvatar: "/placeholder.svg?height=32&width=32",
            content: "Let me know if you need help testing this!",
            createdAt: "2024-01-18",
          },
        ],
      },
    ],
  },
  {
    id: "ISS-002",
    title: "Add dark mode support",
    description: "Implement dark mode theme across the application",
    status: "Open",
    type: "Feature",
    priority: "Medium",
    assignee: "Charlie",
    reporter: "Alice",
    createdAt: "2024-01-18",
    updatedAt: "2024-01-18",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    reporterAvatar: "/placeholder.svg?height=32&width=32",
    likes: 15,
    likedBy: [
      "Alice",
      "Bob",
      "Charlie",
      "David",
      "Eve",
      "Frank",
      "Grace",
      "Henry",
      "Ivy",
      "Jack",
      "Kate",
      "Liam",
      "Mia",
      "Noah",
      "Olivia",
    ],
    comments: [
      {
        id: "c3",
        author: "David",
        authorAvatar: "/placeholder.svg?height=32&width=32",
        content:
          "This would be great! Many users have been requesting this feature.",
        createdAt: "2024-01-19",
      },
    ],
  },
  {
    id: "ISS-003",
    title: "Update user profile UI",
    description: "Redesign user profile page with new components",
    status: "Done",
    type: "Task",
    priority: "Low",
    assignee: "Bob",
    reporter: "Charlie",
    createdAt: "2024-01-10",
    updatedAt: "2024-01-19",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    reporterAvatar: "/placeholder.svg?height=32&width=32",
    likes: 3,
    likedBy: ["Bob", "Alice", "Charlie"],
    changes: [
      {
        field: "status",
        oldValue: "In Review",
        newValue: "Done",
        timestamp: "2024-01-19",
      },
    ],
    comments: [
      {
        id: "c4",
        author: "Charlie",
        authorAvatar: "/placeholder.svg?height=32&width=32",
        content:
          "Great work on the new design! The profile page looks much cleaner now.",
        createdAt: "2024-01-19",
      },
    ],
  },
  {
    id: "ISS-004",
    title: "Database performance optimization",
    description: "Optimize slow queries in the user dashboard",
    status: "Open",
    type: "Improvement",
    priority: "Critical",
    assignee: "Alice",
    reporter: "David",
    createdAt: "2024-01-22",
    updatedAt: "2024-01-22",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    reporterAvatar: "/placeholder.svg?height=32&width=32",
    likes: 12,
    likedBy: [
      "Alice",
      "Bob",
      "Charlie",
      "David",
      "Eve",
      "Frank",
      "Grace",
      "Henry",
      "Ivy",
      "Jack",
      "Kate",
      "Liam",
    ],
  },
  {
    id: "ISS-005",
    title: "Add email notifications",
    description: "Send email notifications for important events",
    status: "In Review",
    type: "Feature",
    priority: "Medium",
    assignee: "Charlie",
    reporter: "Bob",
    createdAt: "2024-01-21",
    updatedAt: "2024-01-23",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    reporterAvatar: "/placeholder.svg?height=32&width=32",
    likes: 6,
    likedBy: ["Bob", "Alice", "David", "Eve", "Frank", "Grace"],
    changes: [
      {
        field: "status",
        oldValue: "In Progress",
        newValue: "In Review",
        timestamp: "2024-01-23",
      },
    ],
  },
  {
    id: "ISS-006",
    title: "Fix mobile responsive issues",
    description: "Several pages are not displaying correctly on mobile devices",
    status: "Blocked",
    type: "Bug",
    priority: "High",
    assignee: "David",
    reporter: "Alice",
    createdAt: "2024-01-16",
    updatedAt: "2024-01-17",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    reporterAvatar: "/placeholder.svg?height=32&width=32",
    likes: 9,
    likedBy: [
      "Alice",
      "Bob",
      "Charlie",
      "David",
      "Eve",
      "Frank",
      "Grace",
      "Henry",
      "Ivy",
    ],
    changes: [
      {
        field: "status",
        oldValue: "In Progress",
        newValue: "Blocked",
        timestamp: "2024-01-17",
      },
    ],
  },
];

const getStatusColor = (status: Issue["status"]) => {
  switch (status) {
    case "Open":
      return "bg-blue-100 text-blue-800";
    case "In Progress":
      return "bg-yellow-100 text-yellow-800";
    case "In Review":
      return "bg-purple-100 text-purple-800";
    case "Done":
      return "bg-green-100 text-green-800";
    case "Blocked":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getPriorityColor = (priority: Issue["priority"]) => {
  switch (priority) {
    case "Critical":
      return "bg-red-100 text-red-800";
    case "High":
      return "bg-orange-100 text-orange-800";
    case "Medium":
      return "bg-yellow-100 text-yellow-800";
    case "Low":
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getTypeIcon = (type: Issue["type"]) => {
  switch (type) {
    case "Bug":
      return <Bug className="h-4 w-4" />;
    case "Feature":
      return <Feature className="h-4 w-4" />;
    case "Task":
      return <CheckSquare className="h-4 w-4" />;
    case "Improvement":
      return <AlertCircle className="h-4 w-4" />;
    default:
      return <CheckSquare className="h-4 w-4" />;
  }
};

const getTypeColor = (type: Issue["type"]) => {
  switch (type) {
    case "Bug":
      return "bg-red-50 text-red-600 border-red-200";
    case "Feature":
      return "bg-blue-50 text-blue-600 border-blue-200";
    case "Task":
      return "bg-green-50 text-green-600 border-green-200";
    case "Improvement":
      return "bg-purple-50 text-purple-600 border-purple-200";
    default:
      return "bg-gray-50 text-gray-600 border-gray-200";
  }
};

export function IssueTracker() {
  const [issues, setIssues] = useState<Issue[]>(mockIssues);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<"createdAt" | "updatedAt">("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [displayedCount, setDisplayedCount] = useState(5);
  const [isLoading, setIsLoading] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const filteredAndSortedIssues = useMemo(() => {
    const filtered = issues.filter((issue) => {
      const matchesSearch =
        issue.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus =
        statusFilter === "all" || issue.status === statusFilter;
      return matchesSearch && matchesStatus;
    });

    // Sort issues
    filtered.sort((a, b) => {
      const dateA = new Date(a[sortBy]).getTime();
      const dateB = new Date(b[sortBy]).getTime();
      return sortOrder === "desc" ? dateB - dateA : dateA - dateB;
    });

    return filtered;
  }, [issues, searchTerm, statusFilter, sortBy, sortOrder]);

  const displayedIssues = filteredAndSortedIssues.slice(0, displayedCount);
  const hasMore = displayedCount < filteredAndSortedIssues.length;

  const loadMore = useCallback(() => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);
    // Simulate loading delay
    setTimeout(() => {
      setDisplayedCount((prev) =>
        Math.min(prev + 5, filteredAndSortedIssues.length)
      );
      setIsLoading(false);
    }, 500);
  }, [isLoading, hasMore, filteredAndSortedIssues.length]);

  // Infinite scroll implementation
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

      if (isNearBottom && hasMore && !isLoading) {
        loadMore();
      }
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [hasMore, isLoading, loadMore]);

  // Reset displayed count when filters change
  useEffect(() => {
    setDisplayedCount(5);
  }, [searchTerm, statusFilter, sortBy, sortOrder]);

  const handleSortToggle = () => {
    setSortOrder(sortOrder === "desc" ? "asc" : "desc");
  };

  const handleAddComment = (issueId: string, content: string) => {
    setIssues((prevIssues) =>
      prevIssues.map((issue) => {
        if (issue.id === issueId) {
          const newComment: Comment = {
            id: `c${Date.now()}`,
            author: "You",
            authorAvatar: "/placeholder.svg?height=32&width=32",
            content,
            createdAt: new Date().toISOString().split("T")[0],
          };
          return {
            ...issue,
            comments: [...(issue.comments || []), newComment],
          };
        }
        return issue;
      })
    );
  };

  const handleLikeIssue = (issueId: string) => {
    setIssues((prevIssues) =>
      prevIssues.map((issue) => {
        if (issue.id === issueId) {
          const isLiked = issue.likedBy.includes("You");
          return {
            ...issue,
            likes: isLiked ? issue.likes - 1 : issue.likes + 1,
            likedBy: isLiked
              ? issue.likedBy.filter((user) => user !== "You")
              : [...issue.likedBy, "You"],
          };
        }
        return issue;
      })
    );
  };

  const renderChangeHistory = (changes: IssueChange[]) => {
    if (!changes || changes.length === 0) return null;

    return (
      <div className="mt-3 p-3 bg-gray-50 rounded-md">
        <div className="flex items-center gap-2 mb-2">
          <History className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium text-muted-foreground">
            Recent Changes
          </span>
        </div>
        <div className="space-y-2">
          {changes.slice(0, 2).map((change, index) => (
            <div key={index} className="flex items-center gap-2 text-sm">
              <span className="capitalize font-medium">{change.field}:</span>
              <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs line-through">
                {change.oldValue}
              </span>
              <ArrowRight className="h-3 w-3 text-muted-foreground" />
              <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                {change.newValue}
              </span>
              <span className="text-muted-foreground text-xs ml-auto">
                {change.timestamp}
              </span>
            </div>
          ))}
          {changes.length > 2 && (
            <div className="text-xs text-muted-foreground">
              +{changes.length - 2} more changes
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckSquare className="h-5 w-5" />
          Issue Tracker
        </CardTitle>
        {/* Filters and Sorting */}{" "}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search issues..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="Open">Open</SelectItem>
              <SelectItem value="In Progress">In Progress</SelectItem>
              <SelectItem value="In Review">In Review</SelectItem>
              <SelectItem value="Done">Done</SelectItem>
              <SelectItem value="Blocked">Blocked</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={sortBy}
            onValueChange={(value: "createdAt" | "updatedAt") =>
              setSortBy(value)
            }
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="createdAt">Created Date</SelectItem>
              <SelectItem value="updatedAt">Updated Date</SelectItem>
            </SelectContent>
          </Select>
          <button
            onClick={handleSortToggle}
            className="flex items-center gap-2 px-3 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-md text-sm"
          >
            <ArrowUpDown className="h-4 w-4" />
            {sortOrder === "desc" ? "Newest" : "Oldest"}
          </button>
        </div>
      </CardHeader>
      <CardContent>
        <div
          ref={containerRef}
          className="max-h-[600px] overflow-y-auto space-y-4"
        >
          {displayedIssues.map((issue) => (
            <div
              key={issue.id}
              className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-muted-foreground">
                    {issue.id}
                  </span>
                  <Badge className={getStatusColor(issue.status)}>
                    {issue.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-3">
                  <div
                    className={`p-1.5 rounded-md border ${getTypeColor(
                      issue.type
                    )}`}
                  >
                    {getTypeIcon(issue.type)}
                  </div>
                  <Badge variant="outline">{issue.type}</Badge>
                  {/* Issue Like Button */}
                  <button
                    onClick={() => handleLikeIssue(issue.id)}
                    className={`flex items-center gap-1 px-2 py-1 rounded-md text-xs hover:bg-red-50 transition-colors ${
                      issue.likedBy.includes("You")
                        ? "text-red-500 bg-red-50"
                        : "text-muted-foreground hover:text-red-500"
                    }`}
                  >
                    <Heart
                      className={`h-4 w-4 ${
                        issue.likedBy.includes("You") ? "fill-current" : ""
                      }`}
                    />
                    <span>{issue.likes}</span>
                  </button>
                </div>
              </div>
              <h3 className="font-semibold mb-2">{issue.title}</h3>
              <p className="text-sm text-muted-foreground mb-4">
                {issue.description}
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Badge className={getPriorityColor(issue.priority)}>
                    {issue.priority}
                  </Badge>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Avatar className="h-6 w-6">
                    <AvatarImage
                      src={issue.assigneeAvatar || "/placeholder.svg"}
                      alt={issue.assignee}
                    />
                    <AvatarFallback className="text-xs">
                      {issue.assignee.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <span>{issue.assignee}</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>Created: {issue.createdAt}</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Avatar className="h-6 w-6">
                    <AvatarImage
                      src={issue.reporterAvatar || "/placeholder.svg"}
                      alt={issue.reporter}
                    />
                    <AvatarFallback className="text-xs">
                      {issue.reporter.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <span>Reporter: {issue.reporter}</span>
                </div>
              </div>
              {issue.updatedAt !== issue.createdAt && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Last updated: {issue.updatedAt}
                </div>
              )}

              {/* Render change history */}
              {renderChangeHistory(issue.changes || [])}

              {/* Comments Section */}
              <CommentsSection
                issueId={issue.id}
                comments={issue.comments}
                onAddComment={handleAddComment}
              />
            </div>
          ))}

          {/* Loading indicator */}
          {isLoading && (
            <div className="flex justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          )}
        </div>

        {filteredAndSortedIssues.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No issues found matching your criteria.
          </div>
        )}

        {/* Results summary */}
        <div className="mt-4 text-sm text-muted-foreground text-center">
          Showing {displayedIssues.length} of {filteredAndSortedIssues.length}{" "}
          issues
          {hasMore && (
            <span className="ml-2 text-primary">
              (Scroll down to load more)
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
