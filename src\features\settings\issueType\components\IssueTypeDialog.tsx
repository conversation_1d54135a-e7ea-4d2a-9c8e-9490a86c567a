"use client";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState } from "react";
import { IssueType } from "@/shared/types/IssueType";
import IssueTypeForm from "./IssueTypeForm";

interface IssueTypeDialogProps {
  mode: "create" | "edit";
  issueType?: IssueType;
  trigger?: React.ReactNode;
}

export function IssueTypeDialog({ mode, issueType, trigger }: IssueTypeDialogProps) {
  const [open, setOpen] = useState(false);
  const handleSuccess = () => {
    setOpen(false);
  };
  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <>
      {trigger ? (
        <div onClick={() => setOpen(true)}>{trigger}</div>
      ) : (
        <Button onClick={() => setOpen(true)}>
          {mode === "create" ? "Tạo Issue Type" : "Sửa Issue Type"}
        </Button>
      )}
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {mode === "create" ? "Tạo Issue Type mới" : `Sửa "${issueType?.name}"`}
            </DialogTitle>
          </DialogHeader>
          <IssueTypeForm
            issueType={issueType}
            mode={mode}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}

export default IssueTypeDialog;
