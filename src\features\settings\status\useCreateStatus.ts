import { statusService } from "@/services/statusService";
import { CreateStatusRequest } from "@/shared/types/status";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export const useCreateStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateStatusRequest) => statusService.createStatus(data),
    onSuccess: (response, variables) => {
      // Invalidate và refetch status list
      queryClient.invalidateQueries({ queryKey: ["useStatus"] });

      toast.success(`Status "${variables.name}" đã được tạo thành công!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi tạo status: ${error.message}`);
    },
  });
};
