import { statusService } from "@/services/statusService";
import { UpdateStatusRequest } from "@/shared/types/Status";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export const useUpdateStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateStatusRequest) => statusService.updateStatus(data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: ["useStatus"] });
      toast.success(
        `Trạng thái "${variables.name}" đã được cập nhật thành công!`
      );
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi cập nhật trạng thái: ${error.message}`);
    },
  });
};
