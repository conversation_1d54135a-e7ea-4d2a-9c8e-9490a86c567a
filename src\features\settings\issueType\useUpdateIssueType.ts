import { issueTypeService } from "@/services/issueTypeService";
import { UpdateIssueTypeRequest } from "@/shared/types/IssueType";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export const useUpdateIssueType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateIssueTypeRequest) =>
      issueTypeService.updateIssueType(data),
    onSuccess(response, variables) {
      if (!response) {
        throw new Error("Không tìm thấy loại vấn đề");
      }
      queryClient.invalidateQueries({ queryKey: ["useIssueType"] });
      toast.success(
        `Loại vấn đề "${variables.name}" đã được cập nhật thành công!`
      );
    },
    onError(error: Error) {
      toast.error(`Lỗi khi cập nhật loại vấn đề: ${error.message}`);
    },
  });
};
