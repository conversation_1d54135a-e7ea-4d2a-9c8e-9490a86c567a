'use client';
import { useState } from "react";
import { Pencil, Mail, ShieldCheck } from "lucide-react";

export default function UserProfileCard() {
  const [user] = useState({
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    avatarUrl: "https://i.pravatar.cc/150?img=12",
  });

  return (
    <div className="max-w-md mx-auto mt-10 p-6 rounded-2xl shadow-md bg-white dark:bg-zinc-900 border border-gray-200 dark:border-zinc-700">
      <div className="flex items-center space-x-4">
       
        <div className="flex-1">
          <h2 className="text-xl font-semibold dark:text-white">{user.name}</h2>
          <p className="text-sm text-gray-500 dark:text-gray-300 flex items-center gap-1">
            <Mail size={14} /> {user.email}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-300 flex items-center gap-1">
            <ShieldCheck size={14} /> Role: {user.role}
          </p>
        </div>
        <button className="text-blue-600 hover:text-blue-800 dark:text-blue-400">
          <Pencil size={20} />
        </button>
      </div>

      <hr className="my-4 border-gray-300 dark:border-zinc-600" />

      <div className="space-y-2">
        <button className="w-full text-left text-sm text-gray-600 hover:text-black dark:text-gray-300 dark:hover:text-white">
          Update Password
        </button>
        <button className="w-full text-left text-sm text-gray-600 hover:text-black dark:text-gray-300 dark:hover:text-white">
          Manage 2FA
        </button>
        <button className="w-full text-left text-sm text-red-600 hover:text-red-800">
          Delete Account
        </button>
      </div>
    </div>
  );
}
