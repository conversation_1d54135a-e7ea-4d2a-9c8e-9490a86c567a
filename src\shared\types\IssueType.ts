export type IssueType = {
  id: number;
  name: string;
  description?: string;
  color: string;
  order: number;
  projectId: number;
  projectKey: string;
  isActive: boolean;
  createdAt: Date;
  createdById: number;
  lastModifiedAt?: Date;
  lastModifiedById?: number;
  deletedAt?: Date;
  deletedById?: number;
};

export type CreateIssueTypeRequest = {
  name: string;
  description?: string;
  color: string;
  order: number;
  active: boolean;
};

export type UpdateIssueTypeRequest = {
  id: number;
  name: string;
  description?: string;
  color: string;
  active: boolean;
};
