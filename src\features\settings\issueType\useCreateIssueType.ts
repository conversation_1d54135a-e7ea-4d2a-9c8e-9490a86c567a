import { issueTypeService } from "@/services/issueTypeService";
import { IssueTypeFormValues } from "../status/schemas/issuetypeFormSchema";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export const useCreateIssueType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: IssueTypeFormValues) =>
      issueTypeService.createIssueType(data),
    onSuccess: (response, variables) => {
      // Invalidate và refetch issue type list
      queryClient.invalidateQueries({ queryKey: ["issueTypes"] });

      toast.success(`Loại vấn đề "${variables.name}" đã được tạo thành công!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi tạo loại vấn đề: ${error.message}`);
    },
  });
};
