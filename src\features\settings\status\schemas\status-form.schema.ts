import { z } from "zod";

export const statusFormSchema = z.object({
  name: z
    .string()
    .min(1, "Tên không được để trống")
    .max(50, "Tên không được quá 50 ký tự")
    .regex(
      /^[a-zA-Z0-9\s\u00C0-\u024F\u1E00-\u1EFF\-_\.]+$/,
      "Tên chỉ được chứa chữ cái, số, khoảng trắng và các ký tự đặc biệt: - _ ."
    ),
  description: z.string().optional(),
  color: z.string().min(1, "<PERSON>àu không được để trống"),
  order: z.number().int().min(1, "Thứ tự không được để trống"),
  active: z.boolean(),
});

export type StatusFormValues = z.infer<typeof statusFormSchema>;
