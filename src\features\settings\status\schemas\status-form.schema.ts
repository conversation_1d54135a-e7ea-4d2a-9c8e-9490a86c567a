import { z } from "zod";

const MIN_NAME_LENGTH = 1;
const MAX_NAME_LENGTH = 50;
const MAX_DESCRIPTION_LENGTH = 500;
const MIN_ORDER = 1;

export const statusFormSchema = z.object({
  name: z
    .string()
    .min(MIN_NAME_LENGTH, "Tên không được để trống")
    .max(MAX_NAME_LENGTH, "Tên không được quá" + MAX_NAME_LENGTH + " ký tự")
    .regex(
      /^[a-zA-Z0-9\s\u00C0-\u024F\u1E00-\u1EFF\-_\.]+$/,
      "Tên chỉ được chứa chữ cái, số, khoảng trắng và các ký tự đặc biệt: - _ ."
    ),
  description: z
    .string()
    .max(
      MAX_DESCRIPTION_LENGTH,
      `<PERSON><PERSON> tả không được quá ${MAX_DESCRIPTION_LENGTH} ký tự`
    )
    .regex(
      /^[a-zA-Z0-9\s\u00C0-\u024F\u1E00-\u1EFF\-_\.]*$/,
      "Mô tả chỉ được chứa chữ cái, số, khoảng trắng và các ký tự đặc biệt: - _ ."
    )
    .optional()
    .default(""),
  color: z
    .string()
    .min(1, "Màu không được để trống")
    .regex(/^#[0-9A-Fa-f]{6}$/, "Màu phải là mã hex hợp lệ (ví dụ: #FF0000)"),
  order: z.number().int().min(MIN_ORDER, "Thứ tự không được để trống"),
  active: z.boolean(),
});

export type StatusFormValues = z.infer<typeof statusFormSchema>;
