import { z } from "zod";
import {
  STATUS_VALIDATION,
  STATUS_ERROR_MESSAGES,
} from "@/shared/constants/statusContants";

export const statusFormSchema = z.object({
  name: z
    .string()
    .min(STATUS_VALIDATION.NAME_MIN_LENGTH, STATUS_ERROR_MESSAGES.NAME_REQUIRED)
    .max(STATUS_VALIDATION.NAME_MAX_LENGTH, STATUS_ERROR_MESSAGES.NAME_TOO_LONG)
    .regex(
      /^[a-zA-Z0-9\s\u00C0-\u024F\u1E00-\u1EFF\-_\.]+$/,
      STATUS_ERROR_MESSAGES.NAME_INVALID_CHARS
    ),
  description: z
    .string()
    .max(
      STATUS_VALIDATION.DESCRIPTION_MAX_LENGTH,
      STATUS_ERROR_MESSAGES.DESCRIPTION_TOO_LONG
    )
    .optional(),

  color: z
    .string()
    .min(1, STATUS_ERROR_MESSAGES.COLOR_INVALID_FORMAT)
    .regex(/^#[0-9A-Fa-f]{6}$/, STATUS_ERROR_MESSAGES.COLOR_INVALID_FORMAT),
  order: z
    .number()
    .int()
    .min(STATUS_VALIDATION.ORDER_MIN, STATUS_ERROR_MESSAGES.ORDER_INVALID),
  active: z.boolean(),
});

export type StatusFormValues = z.infer<typeof statusFormSchema>;
