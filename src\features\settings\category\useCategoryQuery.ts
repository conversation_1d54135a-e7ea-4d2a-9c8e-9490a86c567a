import { useQuery, useQueryClient } from "@tanstack/react-query";
import { categoryService } from "@/services/categoryService";
import {
  Category,
  CategoriesResponse,
  CategoryQueryParams,
  CategoryWithStats,
} from "@/shared/types/categoryTypes";

// Query keys để manage cache
export const categoryQueryKeys = {
  all: ["categories"] as const,
  lists: () => [...categoryQueryKeys.all, "list"] as const,
  list: (params?: CategoryQueryParams) => [...categoryQueryKeys.lists(), params] as const,
  details: () => [...categoryQueryKeys.all, "detail"] as const,
  detail: (id: string) => [...categoryQueryKeys.details(), id] as const,
  stats: () => [...categoryQueryKeys.all, "stats"] as const,
};

// Hook để lấy danh sách categories với pagination
export const useCategoriesQuery = (params?: CategoryQueryParams) => {
  return useQuery({
    queryKey: categoryQueryKeys.list(params),
    queryFn: () => categoryService.getCategories(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook để lấy tất cả categories (không pagination) - dùng cho dropdown
export const useAllCategoriesQuery = () => {
  return useQuery({
    queryKey: categoryQueryKeys.lists(),
    queryFn: () => categoryService.getAllCategories(),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Hook để lấy chi tiết một category
export const useCategoryQuery = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: categoryQueryKeys.detail(id),
    queryFn: () => categoryService.getCategory(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Hook để lấy categories với thống kê
export const useCategoriesWithStatsQuery = () => {
  return useQuery({
    queryKey: categoryQueryKeys.stats(),
    queryFn: () => categoryService.getCategoriesWithStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes (stats change more frequently)
    gcTime: 5 * 60 * 1000,
  });
};

// Hook để prefetch category detail
export const usePrefetchCategory = () => {
  const queryClient = useQueryClient();

  return (id: string) => {
    queryClient.prefetchQuery({
      queryKey: categoryQueryKeys.detail(id),
      queryFn: () => categoryService.getCategory(id),
      staleTime: 5 * 60 * 1000,
    });
  };
};

// Hook để invalidate categories cache
export const useInvalidateCategories = () => {
  const queryClient = useQueryClient();

  return {
    invalidateAll: () => {
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.all });
    },
    invalidateLists: () => {
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.lists() });
    },
    invalidateDetail: (id: string) => {
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.detail(id) });
    },
    invalidateStats: () => {
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.stats() });
    },
  };
};

// Hook để get cached category data
export const useCachedCategory = (id: string): Category | undefined => {
  const queryClient = useQueryClient();

  return queryClient.getQueryData(categoryQueryKeys.detail(id))?.data;
};

// Hook để set category data in cache
export const useSetCategoryCache = () => {
  const queryClient = useQueryClient();

  return (id: string, category: Category) => {
    queryClient.setQueryData(categoryQueryKeys.detail(id), { data: category });
  };
};

// Backward compatibility - hook cũ
export const useCategoryQueryLegacy = () => {
  return useAllCategoriesQuery();
};
