# 🎯 TEMPLATE TẠO FEATURE MỚI

## Bước 1: <PERSON><PERSON><PERSON> nghĩa Requirements
```
- User muốn làm gì?
- Input là gì?
- Output là gì?
- Rules/validation gì?
```

## Bước 2: Tạo Types (<PERSON><PERSON><PERSON> bắt đầu từ đây)
```typescript
// src/shared/types/[feature]Types.ts
export type MyFeature = {
  id: string;
  name: string;
  // ... other fields
};

export type CreateMyFeatureRequest = {
  name: string;
  // ... required fields
};
```

## Bước 3: Tạo Service (Mock data trước)
```typescript
// src/services/[feature]Service.ts
export const myFeatureService = {
  // Luôn bắt đầu với mock data
  getAll: async (): Promise<MyFeature[]> => {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Fake delay
    return [
      { id: "1", name: "Test Item" }
    ];
  },

  create: async (data: CreateMyFeatureRequest): Promise<MyFeature> => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return {
      id: crypto.randomUUID(),
      ...data
    };
  }
};
```

## Bước 4: Tạo Hooks (Logic layer)
```typescript
// src/features/[feature]/hooks/use[Feature].ts
import { useQuery, useMutation } from "@tanstack/react-query";

export const useMyFeatures = () => {
  return useQuery({
    queryKey: ["myFeatures"],
    queryFn: () => myFeatureService.getAll(),
  });
};

export const useCreateMyFeature = () => {
  return useMutation({
    mutationFn: myFeatureService.create,
    onSuccess: () => {
      // Handle success
    }
  });
};
```

## Bước 5: Tạo UI Component (Đơn giản trước)
```typescript
// src/features/[feature]/components/MyFeatureList.tsx
export function MyFeatureList() {
  const { data: items, isLoading } = useMyFeatures();

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      <h1>My Features</h1>
      {items?.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
}
```

## Bước 6: Tạo Page (Kết nối tất cả)
```typescript
// src/app/my-feature/page.tsx
import { MyFeatureList } from "@/features/my-feature/components/MyFeatureList";

export default function MyFeaturePage() {
  return (
    <div className="container mx-auto py-6">
      <MyFeatureList />
    </div>
  );
}
```

---

## 🎯 QUY TẮC VÀNG CHO NGƯỜI MỚI

### ✅ BẮT ĐẦU ĐƠN GIẢN
1. **Chỉ làm 1 chức năng** (ví dụ: chỉ hiển thị list)
2. **Dùng mock data** trước
3. **UI đơn giản** (không cần đẹp)
4. **Không optimize** ngay từ đầu

### ✅ THỨ TỰ VIẾT CODE
1. **Types** → Định nghĩa data structure
2. **Service** → Mock API calls
3. **Hooks** → Business logic
4. **Components** → UI layer
5. **Pages** → Kết nối tất cả

### ✅ KIỂM TRA TỪNG BƯỚC
- Sau mỗi bước → Test ngay
- Có lỗi → Fix trước khi tiếp tục
- Chạy được → Mới thêm feature

### ❌ TRÁNH NHỮNG SAI LẦM
- ❌ Viết quá nhiều code cùng lúc
- ❌ Làm UI đẹp trước khi logic chạy
- ❌ Copy code không hiểu
- ❌ Không test từng bước

---

## 🚀 VÍ DỤ THỰC TẾ: TẠO FEATURE "QUẢN LÝ TAGS"

### Bước 1: Requirements
```
User muốn: Tạo, xem, sửa, xóa tags
Input: Tên tag, màu sắc
Output: Danh sách tags
Rules: Tên không trùng, màu hợp lệ
```

### Bước 2: Bắt đầu viết
```typescript
// 1. Types trước
export type Tag = {
  id: string;
  name: string;
  color: string;
};

// 2. Service với mock data
export const tagService = {
  getAll: async () => [
    { id: "1", name: "Bug", color: "#red" }
  ]
};

// 3. Hook đơn giản
export const useTags = () => {
  return useQuery({
    queryKey: ["tags"],
    queryFn: tagService.getAll
  });
};

// 4. Component cơ bản
export function TagList() {
  const { data: tags } = useTags();
  return (
    <div>
      {tags?.map(tag => <div key={tag.id}>{tag.name}</div>)}
    </div>
  );
}
```

### Bước 3: Test và mở rộng dần
- Chạy được → Thêm create
- Create được → Thêm edit
- Edit được → Thêm delete
- Xong CRUD → Làm đẹp UI

---

## 💡 LỜI KHUYÊN

1. **Đừng sợ code xấu** - Refactor sau
2. **Copy template này** - Thay tên feature
3. **Làm từng bước nhỏ** - Đừng vội
4. **Hỏi khi stuck** - Đừng ngại
5. **Practice nhiều** - Sẽ quen dần

**Remember: Mọi senior dev đều từng là junior. Code phức tạp là do tích lũy kinh nghiệm, không phải tài năng!** 💪
