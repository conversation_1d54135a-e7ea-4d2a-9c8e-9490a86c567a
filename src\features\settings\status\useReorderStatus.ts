"use client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { statusService } from "@/services/statusService";
import { Status } from "@/shared/types/status";
import { toast } from "sonner";

export const useReorderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (statuses: Status[]) => statusService.reorderStatus(statuses),
    onSuccess: () => {
      // Invalidate và refetch status list
      queryClient.invalidateQueries({ queryKey: ["useStatus"] });

      toast.success("Đã cập nhật thứ tự status!");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi sắp xếp status: ${error.message}`);
    },
  });
};
